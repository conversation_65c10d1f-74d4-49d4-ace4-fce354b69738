{"ArrayTypeName": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "*": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "documentation", "nodes"], "DoWhileStatement": ["body", "condition"], "ElementaryTypeNameExpression": ["typeName"], "EmitStatement": ["eventCall"], "EnumDefinition": ["members", "documentation"], "ErrorDefinition": ["documentation", "parameters"], "EventDefinition": ["documentation", "parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "documentation", "modifiers", "overrides", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "ImportDirective": ["symbolA<PERSON>ses"], "$other": ["foreign", "function"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments", "baseName"], "InlineAssembly": ["AST"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "documentation", "overrides", "parameters"], "ModifierInvocation": ["arguments", "modifierName"], "NewExpression": ["typeName"], "OverrideSpecifier": ["overrides"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members", "documentation"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedTypeName": ["pathNode"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["functionList", "libraryName", "typeName"], "VariableDeclaration": ["documentation", "overrides", "typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "YulAssignment": ["value", "variableNames"], "YulBlock": ["statements"], "YulCase": ["body", "value"], "YulExpressionStatement": ["expression"], "YulFunctionCall": ["arguments", "functionName"], "YulForLoop": ["body", "condition", "post", "pre"], "YulFunctionDefinition": ["body", "parameters", "returnVariables"], "YulIf": ["body", "condition"], "YulSwitch": ["cases", "expression"], "YulVariableDeclaration": ["value", "variables"], "SourceUnit": ["nodes"]}, "Assignment": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "BinaryOperation": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "Conditional": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "ElementaryTypeNameExpression": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "ElementaryTypeName": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "ElementaryTypeNameExpression": ["typeName"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "FunctionCall": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "FunctionCallOptions": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "Identifier": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "ImportDirective": ["symbolA<PERSON>ses"], "$other": ["foreign"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments", "modifierName"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "IndexAccess": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "IndexRangeAccess": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "Literal": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "MemberAccess": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "NewExpression": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "TupleExpression": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "UnaryOperation": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "FunctionTypeName": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "ParameterList": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "VariableDeclaration": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "StructuredDocumentation": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "documentation", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "EnumDefinition": ["documentation"], "ErrorDefinition": ["documentation", "parameters"], "EventDefinition": ["documentation", "parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "documentation", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "documentation", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members", "documentation"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["documentation", "typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "OverrideSpecifier": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "overrides", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "overrides", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["overrides", "typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "UserDefinedTypeName": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "overrides", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments", "baseName"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "overrides", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "OverrideSpecifier": ["overrides"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["libraryName", "typeName"], "VariableDeclaration": ["overrides", "typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "IdentifierPath": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "overrides", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments", "baseName"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "overrides", "parameters"], "ModifierInvocation": ["arguments", "modifierName"], "NewExpression": ["typeName"], "OverrideSpecifier": ["overrides"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedTypeName": ["pathNode"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["functionList", "libraryName", "typeName"], "$other": ["function"], "VariableDeclaration": ["overrides", "typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "Mapping": {"ArrayTypeName": ["baseType", "length"], "Assignment": ["leftHandSide", "rightHandSide"], "BinaryOperation": ["leftExpression", "rightExpression"], "Block": ["statements"], "Conditional": ["condition", "falseExpression", "trueExpression"], "ContractDefinition": ["baseContracts", "nodes"], "DoWhileStatement": ["body", "condition"], "EmitStatement": ["eventCall"], "ErrorDefinition": ["parameters"], "EventDefinition": ["parameters"], "ExpressionStatement": ["expression"], "ForStatement": ["body", "condition", "initializationExpression", "loopExpression"], "FunctionCall": ["arguments", "expression"], "FunctionCallOptions": ["expression", "options"], "FunctionDefinition": ["body", "modifiers", "parameters", "returnParameters"], "FunctionTypeName": ["parameterTypes", "returnParameterTypes"], "IfStatement": ["condition", "falseBody", "trueBody"], "IndexAccess": ["baseExpression", "indexExpression"], "IndexRangeAccess": ["baseExpression", "endExpression", "startExpression"], "InheritanceSpecifier": ["arguments"], "Mapping": ["keyType", "valueType"], "MemberAccess": ["expression"], "ModifierDefinition": ["body", "parameters"], "ModifierInvocation": ["arguments"], "NewExpression": ["typeName"], "ParameterList": ["parameters"], "Return": ["expression"], "RevertStatement": ["errorCall"], "StructDefinition": ["members"], "TryCatchClause": ["block", "parameters"], "TryStatement": ["clauses", "externalCall"], "TupleExpression": ["components"], "UnaryOperation": ["subExpression"], "UncheckedBlock": ["statements"], "UserDefinedValueTypeDefinition": ["underlyingType"], "UsingForDirective": ["typeName"], "VariableDeclaration": ["typeName", "value"], "VariableDeclarationStatement": ["declarations", "initialValue"], "WhileStatement": ["body", "condition"], "SourceUnit": ["nodes"]}, "Block": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "Break": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "Continue": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "DoWhileStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "EmitStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "ExpressionStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body", "initializationExpression", "loopExpression"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "ForStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "VariableDeclarationStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body", "initializationExpression"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "IfStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "InlineAssembly": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "YulBlock": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulAssignment": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulFunctionCall": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulAssignment": ["value"], "YulBlock": ["statements"], "YulCase": ["body"], "YulExpressionStatement": ["expression"], "YulFunctionCall": ["arguments"], "YulForLoop": ["body", "condition", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body", "condition"], "YulSwitch": ["cases", "expression"], "YulVariableDeclaration": ["value"], "SourceUnit": ["nodes"]}, "YulIdentifier": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulAssignment": ["value", "variableNames"], "YulBlock": ["statements"], "YulCase": ["body"], "YulExpressionStatement": ["expression"], "YulFunctionCall": ["arguments", "functionName"], "YulForLoop": ["body", "condition", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body", "condition"], "YulSwitch": ["cases", "expression"], "YulVariableDeclaration": ["value"], "SourceUnit": ["nodes"]}, "YulLiteral": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulAssignment": ["value"], "YulBlock": ["statements"], "YulCase": ["body", "value"], "YulExpressionStatement": ["expression"], "YulFunctionCall": ["arguments"], "YulForLoop": ["body", "condition", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body", "condition"], "YulSwitch": ["cases", "expression"], "YulVariableDeclaration": ["value"], "SourceUnit": ["nodes"]}, "YulBreak": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulContinue": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulExpressionStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulLeave": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulForLoop": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulFunctionDefinition": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulTypedName": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body", "parameters", "returnVariables"], "YulIf": ["body"], "YulSwitch": ["cases"], "YulVariableDeclaration": ["variables"], "SourceUnit": ["nodes"]}, "YulIf": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulSwitch": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulCase": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "YulVariableDeclaration": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "InlineAssembly": ["AST"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "YulBlock": ["statements"], "YulCase": ["body"], "YulForLoop": ["body", "post", "pre"], "YulFunctionDefinition": ["body"], "YulIf": ["body"], "YulSwitch": ["cases"], "SourceUnit": ["nodes"]}, "PlaceholderStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "Return": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "RevertStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "TryStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "TryCatchClause": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "UncheckedBlock": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "WhileStatement": {"Block": ["statements"], "ContractDefinition": ["nodes"], "DoWhileStatement": ["body"], "ForStatement": ["body"], "FunctionDefinition": ["body"], "IfStatement": ["falseBody", "trueBody"], "ModifierDefinition": ["body"], "TryCatchClause": ["block"], "TryStatement": ["clauses"], "UncheckedBlock": ["statements"], "WhileStatement": ["body"], "SourceUnit": ["nodes"]}, "ContractDefinition": {"SourceUnit": ["nodes"]}, "InheritanceSpecifier": {"ContractDefinition": ["baseContracts"], "SourceUnit": ["nodes"]}, "EnumDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "EnumValue": {"ContractDefinition": ["nodes"], "EnumDefinition": ["members"], "SourceUnit": ["nodes"]}, "ErrorDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "EventDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "FunctionDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "ModifierInvocation": {"ContractDefinition": ["nodes"], "FunctionDefinition": ["modifiers"], "SourceUnit": ["nodes"]}, "ModifierDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "StructDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "UserDefinedValueTypeDefinition": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "UsingForDirective": {"ContractDefinition": ["nodes"], "SourceUnit": ["nodes"]}, "ImportDirective": {"SourceUnit": ["nodes"]}, "PragmaDirective": {"SourceUnit": ["nodes"]}, "SourceUnit": {}}