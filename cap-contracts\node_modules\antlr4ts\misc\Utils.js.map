{"version": 3, "file": "Utils.js", "sourceRoot": "", "sources": ["../../../src/misc/Utils.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAYH,SAAgB,gBAAgB,CAAC,CAAS,EAAE,YAAqB;IAChE,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAChD,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SACpB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SACpB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACxB,CAAC;AALD,4CAKC;AAED,mDAAmD;AACnD,SAAgB,IAAI,CAAC,UAAyB,EAAE,SAAiB;IAChE,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,KAAK,IAAI,OAAO,IAAI,UAAU,EAAE;QAC/B,IAAI,KAAK,EAAE;YACV,KAAK,GAAG,KAAK,CAAC;SACd;aAAM;YACN,GAAG,IAAI,SAAS,CAAC;SACjB;QAED,GAAG,IAAI,OAAO,CAAC;KACf;IAED,OAAO,GAAG,CAAC;AACZ,CAAC;AAdD,oBAcC;AAED,SAAgB,MAAM,CAAC,CAAwB,EAAE,CAAwB;IACxE,IAAI,CAAC,KAAK,CAAC,EAAE;QACZ,OAAO,IAAI,CAAC;KACZ;IAED,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;QACvC,OAAO,KAAK,CAAC;KACb;IAED,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC;AAVD,wBAUC;AAED,oDAAoD;AACpD,uBAAuB;AACvB,iCAAiC;AACjC,yBAAyB;AACzB,wBAAwB;AACxB,KAAK;AACL,aAAa;AACb,IAAI;AAEJ,8EAA8E;AAC9E,6BAA6B;AAC7B,sDAAsD;AACtD,IAAI;AAEJ,uFAAuF;AACvF,2DAA2D;AAC3D,SAAS;AACT,wBAAwB;AACxB,eAAe;AACf,iBAAiB;AACjB,KAAK;AACL,IAAI;AAEJ,yFAAyF;AACzF,uCAAuC;AACvC,IAAI;AAEJ,qHAAqH;AACrH,sCAAsC;AACtC,yDAAyD;AACzD,gCAAgC;AAChC,2BAA2B;AAC3B,iDAAiD;AACjD,KAAK;AACL,UAAU;AACV,uCAAuC;AACvC,KAAK;AAEL,SAAS;AACT,wBAAwB;AACxB,KAAK;AACL,aAAa;AACb,iBAAiB;AACjB,KAAK;AACL,IAAI;AAEJ,WAAW;AACX,gEAAgE;AAChE,oCAAoC;AACpC,IAAI;AAEJ,WAAW;AACX,4FAA4F;AAC5F,sCAAsC;AACtC,wCAAwC;AACxC,+BAA+B;AAC/B,8DAA8D;AAC9D,2BAA2B;AAC3B,gDAAgD;AAChD,KAAK;AACL,UAAU;AACV,sCAAsC;AACtC,KAAK;AACL,6BAA6B;AAC7B,SAAS;AACT,2BAA2B;AAC3B,qCAAqC;AACrC,2BAA2B;AAC3B,oCAAoC;AACpC,MAAM;AACN,KAAK;AACL,aAAa;AACb,iBAAiB;AACjB,KAAK;AACL,gBAAgB;AAChB,IAAI;AAEJ,uGAAuG;AACvG,uBAAuB;AACvB,yCAAyC;AACzC,gCAAgC;AAChC,iCAAiC;AACjC,mBAAmB;AACnB,yBAAyB;AACzB,OAAO;AAEP,UAAU;AACV,MAAM;AACN,KAAK;AAEL,wBAAwB;AACxB,wCAAwC;AACxC,KAAK;AACL,IAAI;AAEJ,+GAA+G;AAC/G,sCAAsC;AACtC,6CAA6C;AAC7C,YAAY;AACZ,KAAK;AAEL,4EAA4E;AAC5E,oCAAoC;AACpC,gCAAgC;AAChC,wBAAwB;AACxB,MAAM;AACN,KAAK;AACL,IAAI;AAEJ;;GAEG;AACH,SAAgB,KAAK,CAAC,IAAc;IACnC,IAAI,CAAC,GAAwB,IAAI,GAAG,EAAkB,CAAC;IACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClB;IAED,OAAO,CAAC,CAAC;AACV,CAAC;AAPD,sBAOC;AAID,SAAgB,WAAW,CAAC,GAAyB;IACpD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC5B,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAC9B;QAED,OAAO,MAAM,CAAC;KACd;SAAM;QACN,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;KACzB;AACF,CAAC;AAXD,kCAWC;AAED,MAAM;AACN,gBAAgB;AAChB,MAAM;AACN,WAAW;AACX,8DAA8D;AAC9D,4CAA4C;AAC5C,wCAAwC;AACxC,sBAAsB;AACtB,cAAc;AACd,8BAA8B;AAC9B,KAAK;AACL,aAAa;AACb,IAAI", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:45.0833752-07:00\r\n\r\n// Taking a case-by-case approach to pporting this functionaltiy\r\n// as much of it may be supported natively by JavaScript. Or otherwise need\r\n// substantial rethink\r\n\r\nimport { NotNull } from \"../Decorators\";\r\nimport { Equatable } from \"./Stubs\";\r\nimport { IntegerList } from \"./IntegerList\";\r\n\r\nexport function escapeWhitespace(s: string, escapeSpaces: boolean): string {\r\n\treturn escapeSpaces ? s.replace(/ /, \"\\u00B7\") : s\r\n\t\t.replace(/\\t/, \"\\\\t\")\r\n\t\t.replace(/\\n/, \"\\\\n\")\r\n\t\t.replace(/\\r/, \"\\\\r\");\r\n}\r\n\r\n// Seriously: why isn't this built in to java? ugh!\r\nexport function join(collection: Iterable<any>, separator: string): string {\r\n\tlet buf = \"\";\r\n\tlet first = true;\r\n\tfor (let current of collection) {\r\n\t\tif (first) {\r\n\t\t\tfirst = false;\r\n\t\t} else {\r\n\t\t\tbuf += separator;\r\n\t\t}\r\n\r\n\t\tbuf += current;\r\n\t}\r\n\r\n\treturn buf;\r\n}\r\n\r\nexport function equals(x: Equatable | undefined, y: Equatable | undefined): boolean {\r\n\tif (x === y) {\r\n\t\treturn true;\r\n\t}\r\n\r\n\tif (x === undefined || y === undefined) {\r\n\t\treturn false;\r\n\t}\r\n\r\n\treturn x.equals(y);\r\n}\r\n\r\n// export function numNonnull(data: any[]): number {\r\n// \tlet n: number =  0;\r\n// \tif ( data == null ) return n;\r\n// \tfor (let o of data) {\r\n// \t\tif ( o!=null ) n++;\r\n// \t}\r\n// \treturn n;\r\n// }\r\n\r\n// export function removeAllElements<T>(data: Collection<T>, value: T): void {\r\n// \tif ( data==null ) return;\r\n// \twhile ( data.contains(value) ) data.remove(value);\r\n// }\r\n\r\n// export function writeFile(@NotNull file: File, @NotNull content: Uint8Array): void {\r\n// \tlet fos: FileOutputStream = new FileOutputStream(file);\r\n// \ttry {\r\n// \t\tfos.write(content);\r\n// \t} finally {\r\n// \t\tfos.close();\r\n// \t}\r\n// }\r\n\r\n// export function writeFile(@NotNull fileName: string, @NotNull content: string): void {\r\n// \twriteFile(fileName, content, null);\r\n// }\r\n\r\n// export function writeFile(@NotNull fileName: string, @NotNull content: string, @Nullable encoding: string): void {\r\n// \tlet f: File =  new File(fileName);\r\n// \tlet fos: FileOutputStream =  new FileOutputStream(f);\r\n// \tlet osw: OutputStreamWriter;\r\n// \tif (encoding != null) {\r\n// \t\tosw = new OutputStreamWriter(fos, encoding);\r\n// \t}\r\n// \telse {\r\n// \t\tosw = new OutputStreamWriter(fos);\r\n// \t}\r\n\r\n// \ttry {\r\n// \t\tosw.write(content);\r\n// \t}\r\n// \tfinally {\r\n// \t\tosw.close();\r\n// \t}\r\n// }\r\n\r\n// @NotNull\r\n// export function readFile(@NotNull fileName: string): char[] {\r\n// \treturn readFile(fileName, null);\r\n// }\r\n\r\n// @NotNull\r\n// export function readFile(@NotNull fileName: string, @Nullable encoding: string): char[] {\r\n// \tlet f: File =  new File(fileName);\r\n// \tlet size: number =  (int)f.length();\r\n// \tlet isr: InputStreamReader;\r\n// \tlet fis: FileInputStream =  new FileInputStream(fileName);\r\n// \tif ( encoding!=null ) {\r\n// \t\tisr = new InputStreamReader(fis, encoding);\r\n// \t}\r\n// \telse {\r\n// \t\tisr = new InputStreamReader(fis);\r\n// \t}\r\n// \tlet data: char[] =  null;\r\n// \ttry {\r\n// \t\tdata = new char[size];\r\n// \t\tlet n: number =  isr.read(data);\r\n// \t\tif (n < data.length) {\r\n// \t\t\tdata = Arrays.copyOf(data, n);\r\n// \t\t}\r\n// \t}\r\n// \tfinally {\r\n// \t\tisr.close();\r\n// \t}\r\n// \treturn data;\r\n// }\r\n\r\n// export function removeAll<T>(@NotNull predicate: List<T> list,@NotNull Predicate<? super T>): void {\r\n// \tlet j: number =  0;\r\n// \tfor (let i = 0; i < list.size; i++) {\r\n// \t\tlet item: T =  list.get(i);\r\n// \t\tif (!predicate.eval(item)) {\r\n// \t\t\tif (j != i) {\r\n// \t\t\t\tlist.set(j, item);\r\n// \t\t\t}\r\n\r\n// \t\t\tj++;\r\n// \t\t}\r\n// \t}\r\n\r\n// \tif (j < list.size) {\r\n// \t\tlist.subList(j, list.size).clear();\r\n// \t}\r\n// }\r\n\r\n// export function removeAll<T>(@NotNull predicate: Iterable<T> iterable,@NotNull Predicate<? super T>): void {\r\n// \tif (iterable instanceof List<?>) {\r\n// \t\tremoveAll((List<T>)iterable, predicate);\r\n// \t\treturn;\r\n// \t}\r\n\r\n// \tfor (Iterator<T> iterator = iterable.iterator(); iterator.hasNext(); ) {\r\n// \t\tlet item: T =  iterator.next();\r\n// \t\tif (predicate.eval(item)) {\r\n// \t\t\titerator.remove();\r\n// \t\t}\r\n// \t}\r\n// }\r\n\r\n/** Convert array of strings to string&rarr;index map. Useful for\r\n *  converting rulenames to name&rarr;ruleindex map.\r\n */\r\nexport function toMap(keys: string[]): Map<string, number> {\r\n\tlet m: Map<string, number> = new Map<string, number>();\r\n\tfor (let i = 0; i < keys.length; i++) {\r\n\t\tm.set(keys[i], i);\r\n\t}\r\n\r\n\treturn m;\r\n}\r\n\r\nexport function toCharArray(str: string): Uint16Array;\r\nexport function toCharArray(data: IntegerList): Uint16Array;\r\nexport function toCharArray(str: string | IntegerList): Uint16Array {\r\n\tif (typeof str === \"string\") {\r\n\t\tlet result = new Uint16Array(str.length);\r\n\t\tfor (let i = 0; i < str.length; i++) {\r\n\t\t\tresult[i] = str.charCodeAt(i);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t} else {\r\n\t\treturn str.toCharArray();\r\n\t}\r\n}\r\n\r\n// /**\r\n// \t* @since 4.5\r\n// \t*/\r\n// @NotNull\r\n// export function toSet(@NotNull bits: BitSet): IntervalSet {\r\n// \tlet s: IntervalSet =  new IntervalSet();\r\n// \tlet i: number =  bits.nextSetBit(0);\r\n// \twhile ( i >= 0 ) {\r\n// \t\ts.add(i);\r\n// \t\ti = bits.nextSetBit(i+1);\r\n// \t}\r\n// \treturn s;\r\n// }\r\n"]}