import { ContractDefinition, ImportDirective, PragmaDirective, SourceUnit, UsingForDirective } from "solidity-ast";
import { Node } from "solidity-ast/node";
export type DocItem = Exclude<SourceUnit['nodes'][number] | ContractDefinition['nodes'][number], ImportDirective | PragmaDirective | UsingForDirective>;
export declare const docItemTypes: readonly ["ContractDefinition", "EnumDefinition", "ErrorDefinition", "EventDefinition", "FunctionDefinition", "ModifierDefinition", "StructDefinition", "UserDefinedValueTypeDefinition", "VariableDeclaration"];
export declare function isDocItem(node: Node): node is DocItem;
//# sourceMappingURL=doc-item.d.ts.map