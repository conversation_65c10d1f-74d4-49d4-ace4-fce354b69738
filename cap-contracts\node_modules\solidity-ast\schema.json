{"$schema": "http://json-schema.org/draft-06/schema#", "title": "SourceUnit", "type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "absolutePath": {"type": "string"}, "exportedSymbols": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "integer"}}}, "experimentalSolidity": {"type": "boolean"}, "license": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "nodes": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/ContractDefinition"}, {"$ref": "#/definitions/EnumDefinition"}, {"$ref": "#/definitions/ErrorDefinition"}, {"$ref": "#/definitions/FunctionDefinition"}, {"$ref": "#/definitions/ImportDirective"}, {"$ref": "#/definitions/PragmaDirective"}, {"$ref": "#/definitions/StructDefinition"}, {"$ref": "#/definitions/UserDefinedValueTypeDefinition"}, {"$ref": "#/definitions/UsingForDirective"}, {"$ref": "#/definitions/VariableDeclaration"}]}}, "nodeType": {"enum": ["SourceUnit"]}}, "required": ["id", "src", "absolutePath", "exportedSymbols", "nodes", "nodeType"], "definitions": {"SourceLocation": {"type": "string", "pattern": "^\\d+:\\d+:\\d+$"}, "Mutability": {"enum": ["mutable", "immutable", "constant"]}, "StateMutability": {"enum": ["payable", "pure", "nonpayable", "view"]}, "StorageLocation": {"enum": ["calldata", "default", "memory", "storage", "transient"]}, "Visibility": {"enum": ["external", "public", "internal", "private"]}, "TypeDescriptions": {"type": "object", "additionalProperties": false, "properties": {"typeIdentifier": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "typeString": {"anyOf": [{"type": "string"}, {"type": "null"}]}}, "required": []}, "Expression": {"anyOf": [{"$ref": "#/definitions/Assignment"}, {"$ref": "#/definitions/BinaryOperation"}, {"$ref": "#/definitions/Conditional"}, {"$ref": "#/definitions/ElementaryTypeNameExpression"}, {"$ref": "#/definitions/FunctionCall"}, {"$ref": "#/definitions/FunctionCallOptions"}, {"$ref": "#/definitions/Identifier"}, {"$ref": "#/definitions/IndexAccess"}, {"$ref": "#/definitions/IndexRangeAccess"}, {"$ref": "#/definitions/Literal"}, {"$ref": "#/definitions/MemberAccess"}, {"$ref": "#/definitions/NewExpression"}, {"$ref": "#/definitions/TupleExpression"}, {"$ref": "#/definitions/UnaryOperation"}]}, "Statement": {"anyOf": [{"$ref": "#/definitions/Block"}, {"$ref": "#/definitions/Break"}, {"$ref": "#/definitions/Continue"}, {"$ref": "#/definitions/DoWhileStatement"}, {"$ref": "#/definitions/EmitStatement"}, {"$ref": "#/definitions/ExpressionStatement"}, {"$ref": "#/definitions/ForStatement"}, {"$ref": "#/definitions/IfStatement"}, {"$ref": "#/definitions/InlineAssembly"}, {"$ref": "#/definitions/PlaceholderStatement"}, {"$ref": "#/definitions/Return"}, {"$ref": "#/definitions/RevertStatement"}, {"$ref": "#/definitions/TryStatement"}, {"$ref": "#/definitions/UncheckedBlock"}, {"$ref": "#/definitions/VariableDeclarationStatement"}, {"$ref": "#/definitions/WhileStatement"}]}, "TypeName": {"anyOf": [{"$ref": "#/definitions/ArrayTypeName"}, {"$ref": "#/definitions/ElementaryTypeName"}, {"$ref": "#/definitions/FunctionTypeName"}, {"$ref": "#/definitions/Mapping"}, {"$ref": "#/definitions/UserDefinedTypeName"}]}, "ArrayTypeName": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "baseType": {"$ref": "#/definitions/TypeName"}, "length": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "nodeType": {"enum": ["ArrayTypeName"]}}, "required": ["id", "src", "typeDescriptions", "baseType", "nodeType"]}, "Assignment": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "leftHandSide": {"$ref": "#/definitions/Expression"}, "operator": {"enum": ["=", "+=", "-=", "*=", "/=", "%=", "|=", "&=", "^=", ">>=", "<<="]}, "rightHandSide": {"$ref": "#/definitions/Expression"}, "nodeType": {"enum": ["Assignment"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "leftHandSide", "operator", "rightHandSide", "nodeType"]}, "BinaryOperation": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "commonType": {"$ref": "#/definitions/TypeDescriptions"}, "leftExpression": {"$ref": "#/definitions/Expression"}, "operator": {"enum": ["+", "-", "*", "/", "%", "**", "&&", "||", "!=", "==", "<", "<=", ">", ">=", "^", "&", "|", "<<", ">>"]}, "rightExpression": {"$ref": "#/definitions/Expression"}, "function": {"type": "integer"}, "nodeType": {"enum": ["BinaryOperation"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "commonType", "leftExpression", "operator", "rightExpression", "nodeType"]}, "Block": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "statements": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/Statement"}}, {"type": "null"}]}, "nodeType": {"enum": ["Block"]}}, "required": ["id", "src", "nodeType"]}, "Break": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "nodeType": {"enum": ["Break"]}}, "required": ["id", "src", "nodeType"]}, "Conditional": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "condition": {"$ref": "#/definitions/Expression"}, "falseExpression": {"$ref": "#/definitions/Expression"}, "trueExpression": {"$ref": "#/definitions/Expression"}, "nodeType": {"enum": ["Conditional"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "condition", "falseExpression", "trueExpression", "nodeType"]}, "Continue": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "nodeType": {"enum": ["Continue"]}}, "required": ["id", "src", "nodeType"]}, "ContractDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "abstract": {"type": "boolean"}, "baseContracts": {"type": "array", "items": {"$ref": "#/definitions/InheritanceSpecifier"}}, "canonicalName": {"type": "string"}, "contractDependencies": {"type": "array", "items": {"type": "integer"}}, "contractKind": {"enum": ["contract", "interface", "library"]}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "fullyImplemented": {"type": "boolean"}, "linearizedBaseContracts": {"type": "array", "items": {"type": "integer"}}, "nodes": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/EnumDefinition"}, {"$ref": "#/definitions/ErrorDefinition"}, {"$ref": "#/definitions/EventDefinition"}, {"$ref": "#/definitions/FunctionDefinition"}, {"$ref": "#/definitions/ModifierDefinition"}, {"$ref": "#/definitions/StructDefinition"}, {"$ref": "#/definitions/UserDefinedValueTypeDefinition"}, {"$ref": "#/definitions/UsingForDirective"}, {"$ref": "#/definitions/VariableDeclaration"}]}}, "scope": {"type": "integer"}, "usedErrors": {"type": "array", "items": {"type": "integer"}}, "usedEvents": {"type": "array", "items": {"type": "integer"}}, "internalFunctionIDs": {"type": "object", "additionalProperties": {"type": "integer"}}, "nodeType": {"enum": ["ContractDefinition"]}}, "required": ["id", "src", "name", "abstract", "baseContracts", "contractDependencies", "contractKind", "fullyImplemented", "linearizedBaseContracts", "nodes", "scope", "nodeType"]}, "DoWhileStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "body": {"anyOf": [{"$ref": "#/definitions/Block"}, {"$ref": "#/definitions/Statement"}]}, "condition": {"$ref": "#/definitions/Expression"}, "nodeType": {"enum": ["DoWhileStatement"]}}, "required": ["id", "src", "body", "condition", "nodeType"]}, "ElementaryTypeName": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "name": {"type": "string"}, "stateMutability": {"$ref": "#/definitions/StateMutability"}, "nodeType": {"enum": ["ElementaryTypeName"]}}, "required": ["id", "src", "typeDescriptions", "name", "nodeType"]}, "ElementaryTypeNameExpression": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "typeName": {"$ref": "#/definitions/ElementaryTypeName"}, "nodeType": {"enum": ["ElementaryTypeNameExpression"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "typeName", "nodeType"]}, "EmitStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "eventCall": {"$ref": "#/definitions/FunctionCall"}, "nodeType": {"enum": ["EmitStatement"]}}, "required": ["id", "src", "eventCall", "nodeType"]}, "EnumDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "canonicalName": {"type": "string"}, "members": {"type": "array", "items": {"$ref": "#/definitions/EnumValue"}}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "nodeType": {"enum": ["EnumDefinition"]}}, "required": ["id", "src", "name", "canonicalName", "members", "nodeType"]}, "EnumValue": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "nodeType": {"enum": ["EnumValue"]}}, "required": ["id", "src", "name", "nodeType"]}, "ErrorDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "errorSelector": {"type": "string"}, "parameters": {"$ref": "#/definitions/ParameterList"}, "nodeType": {"enum": ["ErrorDefinition"]}}, "required": ["id", "src", "name", "nameLocation", "parameters", "nodeType"]}, "EventDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "anonymous": {"type": "boolean"}, "eventSelector": {"type": "string"}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "parameters": {"$ref": "#/definitions/ParameterList"}, "nodeType": {"enum": ["EventDefinition"]}}, "required": ["id", "src", "name", "anonymous", "parameters", "nodeType"]}, "ExpressionStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "expression": {"$ref": "#/definitions/Expression"}, "nodeType": {"enum": ["ExpressionStatement"]}}, "required": ["id", "src", "expression", "nodeType"]}, "ForStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "body": {"anyOf": [{"$ref": "#/definitions/Block"}, {"$ref": "#/definitions/Statement"}]}, "condition": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "initializationExpression": {"anyOf": [{"anyOf": [{"$ref": "#/definitions/ExpressionStatement"}, {"$ref": "#/definitions/VariableDeclarationStatement"}]}, {"type": "null"}]}, "loopExpression": {"anyOf": [{"$ref": "#/definitions/ExpressionStatement"}, {"type": "null"}]}, "isSimpleCounterLoop": {"type": "boolean"}, "nodeType": {"enum": ["ForStatement"]}}, "required": ["id", "src", "body", "nodeType"]}, "FunctionCall": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "arguments": {"type": "array", "items": {"$ref": "#/definitions/Expression"}}, "expression": {"$ref": "#/definitions/Expression"}, "kind": {"enum": ["functionCall", "typeConversion", "structConstructorCall"]}, "names": {"type": "array", "items": {"type": "string"}}, "nameLocations": {"type": "array", "items": {"type": "string"}}, "tryCall": {"type": "boolean"}, "nodeType": {"enum": ["FunctionCall"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "arguments", "expression", "kind", "names", "tryCall", "nodeType"]}, "FunctionCallOptions": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "expression": {"$ref": "#/definitions/Expression"}, "names": {"type": "array", "items": {"type": "string"}}, "options": {"type": "array", "items": {"$ref": "#/definitions/Expression"}}, "nodeType": {"enum": ["FunctionCallOptions"]}}, "required": ["id", "src", "isConstant", "isPure", "lValueRequested", "typeDescriptions", "expression", "names", "options", "nodeType"]}, "FunctionDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "baseFunctions": {"type": "array", "items": {"type": "integer"}}, "body": {"anyOf": [{"$ref": "#/definitions/Block"}, {"type": "null"}]}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "functionSelector": {"type": "string"}, "implemented": {"type": "boolean"}, "kind": {"enum": ["function", "receive", "constructor", "fallback", "freeFunction"]}, "modifiers": {"type": "array", "items": {"$ref": "#/definitions/ModifierInvocation"}}, "overrides": {"anyOf": [{"$ref": "#/definitions/OverrideSpecifier"}, {"type": "null"}]}, "parameters": {"$ref": "#/definitions/ParameterList"}, "returnParameters": {"$ref": "#/definitions/ParameterList"}, "scope": {"type": "integer"}, "stateMutability": {"$ref": "#/definitions/StateMutability"}, "virtual": {"type": "boolean"}, "visibility": {"$ref": "#/definitions/Visibility"}, "nodeType": {"enum": ["FunctionDefinition"]}}, "required": ["id", "src", "name", "implemented", "kind", "modifiers", "parameters", "returnParameters", "scope", "stateMutability", "virtual", "visibility", "nodeType"]}, "FunctionTypeName": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "parameterTypes": {"$ref": "#/definitions/ParameterList"}, "returnParameterTypes": {"$ref": "#/definitions/ParameterList"}, "stateMutability": {"$ref": "#/definitions/StateMutability"}, "visibility": {"$ref": "#/definitions/Visibility"}, "nodeType": {"enum": ["FunctionTypeName"]}}, "required": ["id", "src", "typeDescriptions", "parameterTypes", "returnParameterTypes", "stateMutability", "visibility", "nodeType"]}, "Identifier": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "name": {"type": "string"}, "overloadedDeclarations": {"type": "array", "items": {"type": "integer"}}, "referencedDeclaration": {"anyOf": [{"type": "integer"}, {"type": "null"}]}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "nodeType": {"enum": ["Identifier"]}}, "required": ["id", "src", "name", "overloadedDeclarations", "typeDescriptions", "nodeType"]}, "IdentifierPath": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocations": {"type": "array", "items": {"type": "string"}}, "referencedDeclaration": {"type": "integer"}, "nodeType": {"enum": ["IdentifierPath"]}}, "required": ["id", "src", "name", "referencedDeclaration", "nodeType"]}, "IfStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "condition": {"$ref": "#/definitions/Expression"}, "falseBody": {"anyOf": [{"anyOf": [{"$ref": "#/definitions/Statement"}, {"$ref": "#/definitions/Block"}]}, {"type": "null"}]}, "trueBody": {"anyOf": [{"$ref": "#/definitions/Statement"}, {"$ref": "#/definitions/Block"}]}, "nodeType": {"enum": ["IfStatement"]}}, "required": ["id", "src", "condition", "trueBody", "nodeType"]}, "ImportDirective": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "absolutePath": {"type": "string"}, "file": {"type": "string"}, "nameLocation": {"type": "string"}, "scope": {"type": "integer"}, "sourceUnit": {"type": "integer"}, "symbolAliases": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"foreign": {"$ref": "#/definitions/Identifier"}, "local": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "nameLocation": {"type": "string"}}, "required": ["foreign"]}}, "unitAlias": {"type": "string"}, "nodeType": {"enum": ["ImportDirective"]}}, "required": ["id", "src", "absolutePath", "file", "scope", "sourceUnit", "symbolA<PERSON>ses", "unitAlias", "nodeType"]}, "IndexAccess": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "baseExpression": {"$ref": "#/definitions/Expression"}, "indexExpression": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "nodeType": {"enum": ["IndexAccess"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "baseExpression", "nodeType"]}, "IndexRangeAccess": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "baseExpression": {"$ref": "#/definitions/Expression"}, "endExpression": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "startExpression": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "nodeType": {"enum": ["IndexRangeAccess"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "baseExpression", "nodeType"]}, "InheritanceSpecifier": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "arguments": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/Expression"}}, {"type": "null"}]}, "baseName": {"anyOf": [{"$ref": "#/definitions/UserDefinedTypeName"}, {"$ref": "#/definitions/IdentifierPath"}]}, "nodeType": {"enum": ["InheritanceSpecifier"]}}, "required": ["id", "src", "baseName", "nodeType"]}, "InlineAssembly": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "AST": {"$ref": "#/definitions/YulBlock"}, "evmVersion": {"enum": ["homestead", "tangerineWhistle", "spurious<PERSON><PERSON><PERSON>", "byzantium", "constantinople", "petersburg", "istanbul", "berlin", "london", "paris", "shanghai", "cancun", "prague"]}, "externalReferences": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"declaration": {"type": "integer"}, "isOffset": {"type": "boolean"}, "isSlot": {"type": "boolean"}, "src": {"$ref": "#/definitions/SourceLocation"}, "valueSize": {"type": "integer"}, "suffix": {"enum": ["slot", "offset", "length"]}}, "required": ["declaration", "isOffset", "isSlot", "src", "valueSize"]}}, "flags": {"type": "array", "items": {"enum": ["memory-safe"]}}, "nodeType": {"enum": ["InlineAssembly"]}}, "required": ["id", "src", "AST", "evmVersion", "externalReferences", "nodeType"]}, "Literal": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "hexValue": {"type": "string", "pattern": "^[0-9a-f]*$"}, "kind": {"enum": ["bool", "number", "string", "hexString", "unicodeString"]}, "subdenomination": {"anyOf": [{"enum": ["seconds", "minutes", "hours", "days", "weeks", "wei", "gwei", "ether", "finney", "s<PERSON><PERSON>"]}, {"type": "null"}]}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "nodeType": {"enum": ["Literal"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "hexValue", "kind", "nodeType"]}, "Mapping": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "keyType": {"$ref": "#/definitions/TypeName"}, "valueType": {"$ref": "#/definitions/TypeName"}, "keyName": {"type": "string"}, "keyNameLocation": {"type": "string"}, "valueName": {"type": "string"}, "valueNameLocation": {"type": "string"}, "nodeType": {"enum": ["Mapping"]}}, "required": ["id", "src", "typeDescriptions", "keyType", "valueType", "nodeType"]}, "MemberAccess": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "expression": {"$ref": "#/definitions/Expression"}, "memberName": {"type": "string"}, "memberLocation": {"type": "string"}, "referencedDeclaration": {"anyOf": [{"type": "integer"}, {"type": "null"}]}, "nodeType": {"enum": ["MemberAccess"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "expression", "memberName", "nodeType"]}, "ModifierDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "baseModifiers": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}]}, "body": {"anyOf": [{"$ref": "#/definitions/Block"}, {"type": "null"}]}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "overrides": {"anyOf": [{"$ref": "#/definitions/OverrideSpecifier"}, {"type": "null"}]}, "parameters": {"$ref": "#/definitions/ParameterList"}, "virtual": {"type": "boolean"}, "visibility": {"$ref": "#/definitions/Visibility"}, "nodeType": {"enum": ["ModifierDefinition"]}}, "required": ["id", "src", "name", "parameters", "virtual", "visibility", "nodeType"]}, "ModifierInvocation": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "arguments": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/Expression"}}, {"type": "null"}]}, "kind": {"enum": ["modifierInvocation", "baseConstructorSpecifier"]}, "modifierName": {"anyOf": [{"$ref": "#/definitions/Identifier"}, {"$ref": "#/definitions/IdentifierPath"}]}, "nodeType": {"enum": ["ModifierInvocation"]}}, "required": ["id", "src", "modifierName", "nodeType"]}, "NewExpression": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "typeName": {"$ref": "#/definitions/TypeName"}, "nodeType": {"enum": ["NewExpression"]}}, "required": ["id", "src", "isConstant", "isPure", "lValueRequested", "typeDescriptions", "typeName", "nodeType"]}, "OverrideSpecifier": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "overrides": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/UserDefinedTypeName"}}, {"type": "array", "items": {"$ref": "#/definitions/IdentifierPath"}}]}, "nodeType": {"enum": ["OverrideSpecifier"]}}, "required": ["id", "src", "overrides", "nodeType"]}, "ParameterList": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/VariableDeclaration"}}, "nodeType": {"enum": ["ParameterList"]}}, "required": ["id", "src", "parameters", "nodeType"]}, "PlaceholderStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "nodeType": {"enum": ["PlaceholderStatement"]}}, "required": ["id", "src", "nodeType"]}, "PragmaDirective": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "literals": {"type": "array", "items": {"type": "string"}}, "nodeType": {"enum": ["PragmaDirective"]}}, "required": ["id", "src", "literals", "nodeType"]}, "Return": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "expression": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "functionReturnParameters": {"type": "integer"}, "nodeType": {"enum": ["Return"]}}, "required": ["id", "src", "functionReturnParameters", "nodeType"]}, "RevertStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "errorCall": {"$ref": "#/definitions/FunctionCall"}, "nodeType": {"enum": ["RevertStatement"]}}, "required": ["id", "src", "errorCall", "nodeType"]}, "StructDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "canonicalName": {"type": "string"}, "members": {"type": "array", "items": {"$ref": "#/definitions/VariableDeclaration"}}, "scope": {"type": "integer"}, "visibility": {"$ref": "#/definitions/Visibility"}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "nodeType": {"enum": ["StructDefinition"]}}, "required": ["id", "src", "name", "canonicalName", "members", "scope", "visibility", "nodeType"]}, "StructuredDocumentation": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "text": {"type": "string"}, "nodeType": {"enum": ["StructuredDocumentation"]}}, "required": ["id", "src", "text", "nodeType"]}, "TryCatchClause": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "block": {"$ref": "#/definitions/Block"}, "errorName": {"type": "string"}, "parameters": {"anyOf": [{"$ref": "#/definitions/ParameterList"}, {"type": "null"}]}, "nodeType": {"enum": ["TryCatchClause"]}}, "required": ["id", "src", "block", "errorName", "nodeType"]}, "TryStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "clauses": {"type": "array", "items": {"$ref": "#/definitions/TryCatchClause"}}, "externalCall": {"$ref": "#/definitions/FunctionCall"}, "nodeType": {"enum": ["TryStatement"]}}, "required": ["id", "src", "clauses", "externalCall", "nodeType"]}, "TupleExpression": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "components": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}}, "isInlineArray": {"type": "boolean"}, "nodeType": {"enum": ["TupleExpression"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "components", "isInlineArray", "nodeType"]}, "UnaryOperation": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "argumentTypes": {"anyOf": [{"type": "array", "items": {"$ref": "#/definitions/TypeDescriptions"}}, {"type": "null"}]}, "isConstant": {"type": "boolean"}, "isLValue": {"type": "boolean"}, "isPure": {"type": "boolean"}, "lValueRequested": {"type": "boolean"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "operator": {"enum": ["++", "--", "-", "!", "delete", "~"]}, "prefix": {"type": "boolean"}, "subExpression": {"$ref": "#/definitions/Expression"}, "function": {"type": "integer"}, "nodeType": {"enum": ["UnaryOperation"]}}, "required": ["id", "src", "isConstant", "isLValue", "isPure", "lValueRequested", "typeDescriptions", "operator", "prefix", "subExpression", "nodeType"]}, "UncheckedBlock": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "statements": {"type": "array", "items": {"$ref": "#/definitions/Statement"}}, "nodeType": {"enum": ["UncheckedBlock"]}}, "required": ["id", "src", "statements", "nodeType"]}, "UserDefinedTypeName": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "contractScope": {"type": "null"}, "name": {"type": "string"}, "pathNode": {"$ref": "#/definitions/IdentifierPath"}, "referencedDeclaration": {"type": "integer"}, "nodeType": {"enum": ["UserDefinedTypeName"]}}, "required": ["id", "src", "typeDescriptions", "referencedDeclaration", "nodeType"]}, "UserDefinedValueTypeDefinition": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "canonicalName": {"type": "string"}, "underlyingType": {"$ref": "#/definitions/TypeName"}, "nodeType": {"enum": ["UserDefinedValueTypeDefinition"]}}, "required": ["id", "src", "name", "underlyingType", "nodeType"]}, "UsingForDirective": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "functionList": {"type": "array", "items": {"anyOf": [{"type": "object", "additionalProperties": false, "properties": {"function": {"$ref": "#/definitions/IdentifierPath"}}, "required": ["function"]}, {"type": "object", "additionalProperties": false, "properties": {"operator": {"enum": ["&", "|", "^", "~", "+", "-", "*", "/", "%", "==", "!=", "<", "<=", ">", ">="]}, "definition": {"$ref": "#/definitions/IdentifierPath"}}, "required": ["operator", "definition"]}]}}, "global": {"type": "boolean"}, "libraryName": {"anyOf": [{"$ref": "#/definitions/UserDefinedTypeName"}, {"$ref": "#/definitions/IdentifierPath"}]}, "typeName": {"anyOf": [{"$ref": "#/definitions/TypeName"}, {"type": "null"}]}, "nodeType": {"enum": ["UsingForDirective"]}}, "required": ["id", "src", "nodeType"]}, "VariableDeclaration": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nameLocation": {"type": "string"}, "baseFunctions": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}]}, "constant": {"type": "boolean"}, "documentation": {"anyOf": [{"$ref": "#/definitions/StructuredDocumentation"}, {"type": "null"}]}, "functionSelector": {"type": "string"}, "indexed": {"type": "boolean"}, "mutability": {"$ref": "#/definitions/Mutability"}, "overrides": {"anyOf": [{"$ref": "#/definitions/OverrideSpecifier"}, {"type": "null"}]}, "scope": {"type": "integer"}, "stateVariable": {"type": "boolean"}, "storageLocation": {"$ref": "#/definitions/StorageLocation"}, "typeDescriptions": {"$ref": "#/definitions/TypeDescriptions"}, "typeName": {"anyOf": [{"$ref": "#/definitions/TypeName"}, {"type": "null"}]}, "value": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "visibility": {"$ref": "#/definitions/Visibility"}, "nodeType": {"enum": ["VariableDeclaration"]}}, "required": ["id", "src", "name", "constant", "mutability", "scope", "stateVariable", "storageLocation", "typeDescriptions", "visibility", "nodeType"]}, "VariableDeclarationStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "assignments": {"type": "array", "items": {"anyOf": [{"type": "integer"}, {"type": "null"}]}}, "declarations": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/VariableDeclaration"}, {"type": "null"}]}}, "initialValue": {"anyOf": [{"$ref": "#/definitions/Expression"}, {"type": "null"}]}, "nodeType": {"enum": ["VariableDeclarationStatement"]}}, "required": ["id", "src", "assignments", "declarations", "nodeType"]}, "WhileStatement": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "src": {"$ref": "#/definitions/SourceLocation"}, "documentation": {"type": "string"}, "body": {"anyOf": [{"$ref": "#/definitions/Block"}, {"$ref": "#/definitions/Statement"}]}, "condition": {"$ref": "#/definitions/Expression"}, "nodeType": {"enum": ["WhileStatement"]}}, "required": ["id", "src", "body", "condition", "nodeType"]}, "YulStatement": {"anyOf": [{"$ref": "#/definitions/YulAssignment"}, {"$ref": "#/definitions/YulBlock"}, {"$ref": "#/definitions/YulBreak"}, {"$ref": "#/definitions/YulContinue"}, {"$ref": "#/definitions/YulExpressionStatement"}, {"$ref": "#/definitions/YulLeave"}, {"$ref": "#/definitions/YulForLoop"}, {"$ref": "#/definitions/YulFunctionDefinition"}, {"$ref": "#/definitions/YulIf"}, {"$ref": "#/definitions/YulSwitch"}, {"$ref": "#/definitions/YulVariableDeclaration"}]}, "YulExpression": {"anyOf": [{"$ref": "#/definitions/YulFunctionCall"}, {"$ref": "#/definitions/YulIdentifier"}, {"$ref": "#/definitions/YulLiteral"}]}, "YulLiteral": {"anyOf": [{"$ref": "#/definitions/YulLiteralValue"}, {"$ref": "#/definitions/YulLiteralHexValue"}]}, "YulLiteralValue": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "value": {"type": "string"}, "kind": {"enum": ["number", "string", "bool"]}, "type": {"type": "string"}, "nodeType": {"enum": ["Yu<PERSON><PERSON><PERSON><PERSON>"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "value", "kind", "type", "nodeType"]}, "YulLiteralHexValue": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "hexValue": {"type": "string"}, "kind": {"enum": ["number", "string", "bool"]}, "type": {"type": "string"}, "value": {"type": "string"}, "nodeType": {"enum": ["Yu<PERSON><PERSON><PERSON><PERSON>"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "hexValue", "kind", "type", "nodeType"]}, "YulAssignment": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "value": {"$ref": "#/definitions/YulExpression"}, "variableNames": {"type": "array", "items": {"$ref": "#/definitions/YulIdentifier"}}, "nodeType": {"enum": ["YulAssignment"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "value", "variableNames", "nodeType"]}, "YulBlock": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "statements": {"type": "array", "items": {"$ref": "#/definitions/YulStatement"}}, "nodeType": {"enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "statements", "nodeType"]}, "YulBreak": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "nodeType": {"enum": ["YulBreak"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "nodeType"]}, "YulCase": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "body": {"$ref": "#/definitions/YulBlock"}, "value": {"anyOf": [{"enum": ["default"]}, {"$ref": "#/definitions/YulLiteral"}]}, "nodeType": {"enum": ["YulCase"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "body", "value", "nodeType"]}, "YulContinue": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "nodeType": {"enum": ["YulContinue"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "nodeType"]}, "YulExpressionStatement": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "expression": {"$ref": "#/definitions/YulExpression"}, "nodeType": {"enum": ["YulExpressionStatement"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "expression", "nodeType"]}, "YulFunctionCall": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "arguments": {"type": "array", "items": {"$ref": "#/definitions/YulExpression"}}, "functionName": {"$ref": "#/definitions/YulIdentifier"}, "nodeType": {"enum": ["YulFunctionCall"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "arguments", "functionName", "nodeType"]}, "YulForLoop": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "body": {"$ref": "#/definitions/YulBlock"}, "condition": {"$ref": "#/definitions/YulExpression"}, "post": {"$ref": "#/definitions/YulBlock"}, "pre": {"$ref": "#/definitions/YulBlock"}, "nodeType": {"enum": ["YulForLoop"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "body", "condition", "post", "pre", "nodeType"]}, "YulFunctionDefinition": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "body": {"$ref": "#/definitions/YulBlock"}, "name": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/YulTypedName"}}, "returnVariables": {"type": "array", "items": {"$ref": "#/definitions/YulTypedName"}}, "nodeType": {"enum": ["YulFunctionDefinition"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "body", "name", "nodeType"]}, "YulIdentifier": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "nodeType": {"enum": ["YulIdentifier"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "name", "nodeType"]}, "YulIf": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "body": {"$ref": "#/definitions/YulBlock"}, "condition": {"$ref": "#/definitions/YulExpression"}, "nodeType": {"enum": ["YulIf"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "body", "condition", "nodeType"]}, "YulLeave": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "nodeType": {"enum": ["YulLeave"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "nodeType"]}, "YulSwitch": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "cases": {"type": "array", "items": {"$ref": "#/definitions/YulCase"}}, "expression": {"$ref": "#/definitions/YulExpression"}, "nodeType": {"enum": ["YulSwitch"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "cases", "expression", "nodeType"]}, "YulTypedName": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "name": {"type": "string"}, "type": {"type": "string"}, "nodeType": {"enum": ["YulTypedName"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "name", "type", "nodeType"]}, "YulVariableDeclaration": {"type": "object", "additionalProperties": false, "properties": {"src": {"$ref": "#/definitions/SourceLocation"}, "value": {"anyOf": [{"$ref": "#/definitions/YulExpression"}, {"type": "null"}]}, "variables": {"type": "array", "items": {"$ref": "#/definitions/YulTypedName"}}, "nodeType": {"enum": ["YulVariableDeclaration"]}, "nativeSrc": {"$ref": "#/definitions/SourceLocation"}}, "required": ["src", "variables", "nodeType"]}}}