// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.245.2/containers/go
{
	"name": "Ubuntu",
	"build": {
		"dockerfile": "Dockerfile",
	},
	// Configure tool-specific properties.
	"customizations": {
		// Configure access control to other repositories
		"codespaces": {
			"repositories": {
				"Layr-Labs/*": {
					"permissions": "write-all"
				}
			}
		},
		// Configure properties specific to VS Code.
		"vscode": {
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"NomicFoundation.hardhat-solidity",
				"GitHub.copilot"
			]
		}
	},
	"containerEnv": {
		"PRIVATE_KEY": "${localEnv:PRIVATE_KEY}",
		"PUBLIC_KEY": "${localEnv:PUBLIC_KEY}",
		"RPC_MAINNET": "${localEnv:RPC_MAINNET}",
		"RPC_HOLESKY": "${localEnv:RPC_HOLESKY}"
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],
	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "chmod +x ./.devcontainer/install.sh && bash ./.devcontainer/install.sh",
	// Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode"
}