{"version": 3, "file": "Stubs.js", "sourceRoot": "", "sources": ["../../../src/misc/Stubs.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport interface Equatable {\r\n\tequals(other: any): boolean;\r\n\thashCode(): number;\r\n}\r\n\r\nexport interface Comparable<T> {\r\n\tcompareTo(o: T): number;\r\n}\r\n\r\n// This has been tweaked to fore implemenations to support either Java or JavaScript collections passed in...\r\n\r\nexport interface JavaCollection<E> extends Iterable<E>, Equatable {\r\n\tadd(e: E): boolean;\r\n\taddAll(collection: Iterable<E>): boolean;\r\n\tclear(): void;\r\n\tcontains(o: any): boolean;                         // Shouldn't argument be restricted to E?\r\n\tcontainsAll(collection: Iterable<any>): boolean; // Shouldn't argument be restricted to Collection<E>?\r\n\treadonly isEmpty: boolean;\r\n\treadonly size: number;\r\n\ttoArray(): E[];\r\n}\r\n\r\nexport interface JavaSet<E> extends JavaCollection<E> {\r\n\t// Seems like Java's Set doesn't really seem to extend Java's Collection with anything...\r\n\r\n\t// add(e:E): boolean;\r\n\t// addAll(collection:Iterable<E>): boolean;\r\n\t// clear(): void;\r\n\t// contains(o:any): boolean;               // Shouldn't argument be restricted to E?\r\n\t// containsAll(collection: Iterable<any>)  // Shouldn't argument be restricted to E?\r\n\t// readonly isEmpty: boolean;\r\n\t// readonly size: number;\r\n\t// toArray(): E[];\r\n}\r\n\r\nexport interface JavaMap<K, V> extends Equatable {\r\n\tclear(): void;\r\n\tcontainsKey(key: K): boolean;\r\n\tget(key: K): V | undefined;\r\n\treadonly isEmpty: boolean;\r\n\tput(key: K, value: V): V | undefined;\r\n\treadonly size: number;\r\n}\r\n"]}