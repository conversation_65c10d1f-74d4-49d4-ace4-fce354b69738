{"name": "postinstall", "version": "0.8.0", "description": "Deploy files from modules after npm install", "main": "index.js", "scripts": {"test": "mocha"}, "bin": {"postinstall": "bin/postinstall.js"}, "repository": {"type": "git", "url": "git+https://github.com/kapouer/postinstall.git"}, "keywords": ["postinstall", "npm", "deploy"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/kapouer/postinstall/issues"}, "homepage": "https://github.com/kapouer/postinstall#readme", "dependencies": {"@danieldietrich/copy": "^0.4.2", "glob": "^8.0.3", "minimist": "^1.2.6", "resolve-from": "^5.0.0", "resolve-pkg": "^2.0.0"}, "devDependencies": {"@kapouer/eslint-config": "^1.8.0", "fs-extra": "^10.1.0", "mocha": ">=10.0.0"}, "mocha": {"spec": "test/unit.js"}, "eslintConfig": {"extends": "@kapouer/eslint-config", "ignorePatterns": ["test/fixtures/**"], "overrides": [{"files": ["index.js"], "env": {"es6": true}}, {"files": ["test/*.js"], "env": {"mocha": true}}]}}