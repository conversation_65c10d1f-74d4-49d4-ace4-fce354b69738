{"name": "path-is-absolute", "version": "1.0.1", "description": "Node.js 0.12 path.isAbsolute() ponyfill", "license": "MIT", "repository": "sindresorhus/path-is-absolute", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["path", "paths", "file", "dir", "absolute", "isabsolute", "is-absolute", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim", "is", "detect", "check"], "devDependencies": {"xo": "^0.16.0"}}