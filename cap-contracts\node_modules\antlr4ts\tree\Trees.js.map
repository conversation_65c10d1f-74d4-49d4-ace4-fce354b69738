{"version": 3, "file": "Trees.js", "sourceRoot": "", "sources": ["../../../src/tree/Trees.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,oCAAiC;AACjC,gDAA6C;AAC7C,2CAAwC;AAExC,8CAAwC;AACxC,sCAAmC;AACnC,4DAAyD;AAGzD,yCAAsC;AACtC,iDAA8C;AAC9C,oCAAiC;AAEjC,uCAAuC;AAEvC,qEAAqE;AACrE,MAAa,KAAK;IAmBV,MAAM,CAAC,YAAY,CAAU,CAAO,EAAE,IAAwB;QACpE,IAAI,SAA+B,CAAC;QACpC,IAAI,IAAI,YAAY,eAAM,EAAE;YAC3B,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SAC3B;aAAM;YACN,SAAS,GAAG,IAAI,CAAC;SACjB;QAED,IAAI,CAAC,GAAW,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,EAAE;YACvB,OAAO,CAAC,CAAC;SACT;QACD,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,CAAC,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;QAClE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACX,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACV,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;aACb;YACD,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;SACrD;QACD,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,GAAG,CAAC;IACZ,CAAC;IAIM,MAAM,CAAC,WAAW,CAAC,CAAO,EAAE,IAAmC;QACrE,IAAI,SAA+B,CAAC;QACpC,IAAI,IAAI,YAAY,eAAM,EAAE;YAC3B,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SAC3B;aAAM,IAAI,IAAI,EAAE;YAChB,SAAS,GAAG,IAAI,CAAC;SACjB;aAAM;YACN,yBAAyB;YACzB,IAAI,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;YACxB,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACrC,OAAO,OAAO,CAAC,IAAI,CAAC;aACpB;YACD,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,YAAY,mBAAQ,EAAE;YAC1B,IAAI,WAAW,GAAgB,CAAC,CAAC,WAAW,CAAC;YAC7C,IAAI,SAAS,GAAW,WAAW,CAAC,SAAS,CAAC;YAC9C,IAAI,QAAQ,GAAW,SAAS,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,SAAS,GAAW,WAAW,CAAC,SAAS,CAAC;YAC9C,IAAI,SAAS,KAAK,SAAG,CAAC,kBAAkB,EAAE;gBACzC,OAAO,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC;aAClC;YACD,OAAO,QAAQ,CAAC;SAChB;aACI,IAAI,CAAC,YAAY,qBAAS,EAAE;YAChC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;SACpB;aACI,IAAI,CAAC,YAAY,2BAAY,EAAE;YACnC,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;YACtB,OAAO,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;SACzB;QACD,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;IAC7C,CAAC;IAKM,MAAM,CAAC,WAAW,CAAC,CAAO;QAChC,IAAI,IAAI,GAAW,EAAE,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACzB;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAUM,MAAM,CAAC,YAAY,CAAU,CAAO;QAC1C,IAAI,SAAS,GAAW,EAAE,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE;YACT,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;YACxC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;SACb;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,YAAY,CAAC,CAAO,EAAE,CAAO;QAC1C,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;YAC1B,OAAO,KAAK,CAAC;SACb;QACD,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE;YACT,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,OAAO,IAAI,CAAC;aACZ;YACD,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,CAAY,EAAE,KAAa;QAC1D,OAAO,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,CAAY,EAAE,SAAiB;QAC7D,OAAO,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,CAAY,EAAE,KAAa,EAAE,UAAmB;QAC1E,IAAI,KAAK,GAAgB,EAAE,CAAC;QAC5B,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,CAAY,EAAE,KAAa,EAAE,UAAmB,EAAE,KAAkB;QAC/F,mCAAmC;QACnC,IAAI,UAAU,IAAI,CAAC,YAAY,2BAAY,EAAE;YAC5C,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;gBAC5B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACd;SACD;aACI,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,qCAAiB,EAAE;YACvD,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,EAAE;gBAC1B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACd;SACD;QACD,iBAAiB;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;SAC7D;IACF,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,cAAc,CAAC,CAAY;QACxC,IAAI,KAAK,GAAgB,EAAE,CAAC;QAE5B,SAAS,OAAO,CAAC,CAAY;YAC5B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aACvB;QACF,CAAC;QAED,OAAO,CAAC,CAAC,CAAC,CAAC;QACX,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,+BAA+B,CACnC,CAAY,EACrB,eAAuB,EAAE,YAAY;IACrC,cAAsB;QAEtB,IAAI,CAAC,GAAW,CAAC,CAAC,UAAU,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,KAAK,GAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,GAAG,KAAK,CAAC,+BAA+B,CAAC,KAAK,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;YACtF,IAAI,CAAC,EAAE;gBACN,OAAO,CAAC,CAAC;aACT;SACD;QACD,IAAI,CAAC,YAAY,qCAAiB,EAAE;YACnC,IAAI,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC;YACvB,IAAI,eAAe,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,iCAAiC;gBAC7E,CAAC,SAAS,IAAI,IAAI,IAAI,cAAc,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE;gBAC/D,kGAAkG;gBAClG,OAAO,CAAC,CAAC;aACT;SACD;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,uBAAuB,CACpC,CAAoB,EACpB,IAAuB,EACvB,UAAkB,EAClB,SAAiB;QACjB,IAAI,CAAC,CAAC,EAAE;YACP,OAAO;SACP;QACD,IAAI,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,KAAK,GAAa,KAAK,CAAC,cAAc,CAAC;YAC3C,IAAI,KAAK,YAAY,qCAAiB,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,IAAI,KAAK,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE;gBACxF,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,sDAAsD;oBAC5F,IAAI,MAAM,GAAgB,IAAI,yBAAW,CAAC,aAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;oBACrE,CAAC,CAAC,QAAS,CAAC,CAAC,CAAC,GAAG,IAAI,2BAAY,CAAC,MAAM,CAAC,CAAC,CAAC,yBAAyB;iBACpE;aACD;SACD;IACF,CAAC;IAQM,MAAM,CAAC,gBAAgB,CAAC,CAAO,EAAE,IAAkC;QACzE,gFAAgF;QAChF,IAAI,IAAI,CAAC,CAAc,CAAC,EAAE;YACzB,OAAO,CAAC,CAAC;SACT;QAED,IAAI,CAAC,GAAY,CAAC,CAAC,UAAU,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAG,CAAC,GAAG,CAAC,EAAG,CAAC,EAAE,EAAC;YAC5B,IAAI,CAAC,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAA+B,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,SAAS,EAAE;gBACpB,OAAO,CAAC,CAAC;aACT;SACD;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;CACD;AAnPA;IAA4B,WAAA,oBAAO,CAAA;+BAyBlC;AA0DD;IADC,oBAAO;IACoB,WAAA,oBAAO,CAAA;+BAQlC;AA6ED;IACE,WAAA,oBAAO,CAAA;kDAqBR;AAjNF,sBAsQC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:48.3187865-07:00\r\n\r\nimport { Arrays } from \"../misc/Arrays\";\r\nimport { ATN } from \"../atn/ATN\";\r\nimport { CommonToken } from \"../CommonToken\";\r\nimport { ErrorNode } from \"./ErrorNode\";\r\nimport { Interval } from \"../misc/Interval\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { Parser } from \"../Parser\";\r\nimport { ParserRuleContext } from \"../ParserRuleContext\";\r\nimport { ParseTree } from \"./ParseTree\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { RuleNode } from \"./RuleNode\";\r\nimport { TerminalNode } from \"./TerminalNode\";\r\nimport { Token } from \"../Token\";\r\nimport { Tree } from \"./Tree\";\r\nimport * as Utils from \"../misc/Utils\";\r\n\r\n/** A set of utility routines useful for all kinds of ANTLR trees. */\r\nexport class Trees {\r\n\t/** Print out a whole tree in LISP form. {@link #getNodeText} is used on the\r\n\t *  node payloads to get the text for the nodes.  Detect\r\n\t *  parse trees and extract data appropriately.\r\n\t */\r\n\tpublic static toStringTree(/*@NotNull*/ t: Tree): string;\r\n\r\n\t/** Print out a whole tree in LISP form. {@link #getNodeText} is used on the\r\n\t *  node payloads to get the text for the nodes.  Detect\r\n\t *  parse trees and extract data appropriately.\r\n\t */\r\n\tpublic static toStringTree(/*@NotNull*/ t: Tree, recog: Parser | undefined): string;\r\n\r\n\t/** Print out a whole tree in LISP form. {@link #getNodeText} is used on the\r\n\t *  node payloads to get the text for the nodes.\r\n\t */\r\n\tpublic static toStringTree(/*@NotNull*/ t: Tree, /*@Nullable*/ ruleNames: string[] | undefined): string;\r\n\r\n\tpublic static toStringTree(/*@NotNull*/ t: Tree, arg2?: Parser | string[]): string;\r\n\tpublic static toStringTree(@NotNull t: Tree, arg2?: Parser | string[]): string {\r\n\t\tlet ruleNames: string[] | undefined;\r\n\t\tif (arg2 instanceof Parser) {\r\n\t\t\truleNames = arg2.ruleNames;\r\n\t\t} else {\r\n\t\t\truleNames = arg2;\r\n\t\t}\r\n\r\n\t\tlet s: string = Utils.escapeWhitespace(this.getNodeText(t, ruleNames), false);\r\n\t\tif (t.childCount === 0) {\r\n\t\t\treturn s;\r\n\t\t}\r\n\t\tlet buf = \"\";\r\n\t\tbuf += (\"(\");\r\n\t\ts = Utils.escapeWhitespace(this.getNodeText(t, ruleNames), false);\r\n\t\tbuf += (s);\r\n\t\tbuf += (\" \");\r\n\t\tfor (let i = 0; i < t.childCount; i++) {\r\n\t\t\tif (i > 0) {\r\n\t\t\t\tbuf += (\" \");\r\n\t\t\t}\r\n\t\t\tbuf += (this.toStringTree(t.getChild(i), ruleNames));\r\n\t\t}\r\n\t\tbuf += (\")\");\r\n\t\treturn buf;\r\n\t}\r\n\r\n\tpublic static getNodeText(/*@NotNull*/ t: Tree, recog: Parser | undefined): string;\r\n\tpublic static getNodeText(/*@NotNull*/ t: Tree, ruleNames: string[] | undefined): string;\r\n\tpublic static getNodeText(t: Tree, arg2: Parser | string[] | undefined): string {\r\n\t\tlet ruleNames: string[] | undefined;\r\n\t\tif (arg2 instanceof Parser) {\r\n\t\t\truleNames = arg2.ruleNames;\r\n\t\t} else if (arg2) {\r\n\t\t\truleNames = arg2;\r\n\t\t} else {\r\n\t\t\t// no recog or rule names\r\n\t\t\tlet payload = t.payload;\r\n\t\t\tif (typeof payload.text === \"string\") {\r\n\t\t\t\treturn payload.text;\r\n\t\t\t}\r\n\t\t\treturn t.payload.toString();\r\n\t\t}\r\n\r\n\t\tif (t instanceof RuleNode) {\r\n\t\t\tlet ruleContext: RuleContext = t.ruleContext;\r\n\t\t\tlet ruleIndex: number = ruleContext.ruleIndex;\r\n\t\t\tlet ruleName: string = ruleNames[ruleIndex];\r\n\t\t\tlet altNumber: number = ruleContext.altNumber;\r\n\t\t\tif (altNumber !== ATN.INVALID_ALT_NUMBER) {\r\n\t\t\t\treturn ruleName + \":\" + altNumber;\r\n\t\t\t}\r\n\t\t\treturn ruleName;\r\n\t\t}\r\n\t\telse if (t instanceof ErrorNode) {\r\n\t\t\treturn t.toString();\r\n\t\t}\r\n\t\telse if (t instanceof TerminalNode) {\r\n\t\t\tlet symbol = t.symbol;\r\n\t\t\treturn symbol.text || \"\";\r\n\t\t}\r\n\t\tthrow new TypeError(\"Unexpected node type\");\r\n\t}\r\n\r\n\t/** Return ordered list of all children of this node */\r\n\tpublic static getChildren(t: ParseTree): ParseTree[];\r\n\tpublic static getChildren(t: Tree): Tree[];\r\n\tpublic static getChildren(t: Tree): Tree[] {\r\n\t\tlet kids: Tree[] = [];\r\n\t\tfor (let i = 0; i < t.childCount; i++) {\r\n\t\t\tkids.push(t.getChild(i));\r\n\t\t}\r\n\t\treturn kids;\r\n\t}\r\n\r\n\t/** Return a list of all ancestors of this node.  The first node of\r\n\t *  list is the root and the last is the parent of this node.\r\n\t *\r\n\t *  @since 4.5.1\r\n\t */\r\n\tpublic static getAncestors(t: ParseTree): ParseTree[];\r\n\tpublic static getAncestors(t: Tree): Tree[];\r\n\t@NotNull\r\n\tpublic static getAncestors(@NotNull t: Tree): Tree[] {\r\n\t\tlet ancestors: Tree[] = [];\r\n\t\tlet p = t.parent;\r\n\t\twhile (p) {\r\n\t\t\tancestors.unshift(p); // insert at start\r\n\t\t\tp = p.parent;\r\n\t\t}\r\n\t\treturn ancestors;\r\n\t}\r\n\r\n\t/** Return true if t is u's parent or a node on path to root from u.\r\n\t *  Use === not equals().\r\n\t *\r\n\t *  @since 4.5.1\r\n\t */\r\n\tpublic static isAncestorOf(t: Tree, u: Tree): boolean {\r\n\t\tif (!t || !u || !t.parent) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tlet p = u.parent;\r\n\t\twhile (p) {\r\n\t\t\tif (t === p) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tp = p.parent;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\tpublic static findAllTokenNodes(t: ParseTree, ttype: number): ParseTree[] {\r\n\t\treturn Trees.findAllNodes(t, ttype, true);\r\n\t}\r\n\r\n\tpublic static findAllRuleNodes(t: ParseTree, ruleIndex: number): ParseTree[] {\r\n\t\treturn Trees.findAllNodes(t, ruleIndex, false);\r\n\t}\r\n\r\n\tpublic static findAllNodes(t: ParseTree, index: number, findTokens: boolean): ParseTree[] {\r\n\t\tlet nodes: ParseTree[] = [];\r\n\t\tTrees._findAllNodes(t, index, findTokens, nodes);\r\n\t\treturn nodes;\r\n\t}\r\n\r\n\tpublic static _findAllNodes(t: ParseTree, index: number, findTokens: boolean, nodes: ParseTree[]): void {\r\n\t\t// check this node (the root) first\r\n\t\tif (findTokens && t instanceof TerminalNode) {\r\n\t\t\tif (t.symbol.type === index) {\r\n\t\t\t\tnodes.push(t);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (!findTokens && t instanceof ParserRuleContext) {\r\n\t\t\tif (t.ruleIndex === index) {\r\n\t\t\t\tnodes.push(t);\r\n\t\t\t}\r\n\t\t}\r\n\t\t// check children\r\n\t\tfor (let i = 0; i < t.childCount; i++) {\r\n\t\t\tTrees._findAllNodes(t.getChild(i), index, findTokens, nodes);\r\n\t\t}\r\n\t}\r\n\r\n\t/** Get all descendents; includes t itself.\r\n\t *\r\n\t * @since 4.5.1\r\n\t */\r\n\tpublic static getDescendants(t: ParseTree): ParseTree[] {\r\n\t\tlet nodes: ParseTree[] = [];\r\n\r\n\t\tfunction recurse(e: ParseTree): void {\r\n\t\t\tnodes.push(e);\r\n\t\t\tconst n = e.childCount;\r\n\t\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\t\trecurse(e.getChild(i));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\trecurse(t);\r\n\t\treturn nodes;\r\n\t}\r\n\r\n\t/** Find smallest subtree of t enclosing range startTokenIndex..stopTokenIndex\r\n\t *  inclusively using postorder traversal.  Recursive depth-first-search.\r\n\t *\r\n\t *  @since 4.5\r\n\t */\r\n\tpublic static getRootOfSubtreeEnclosingRegion(\r\n\t\t@NotNull t: ParseTree,\r\n\t\tstartTokenIndex: number, // inclusive\r\n\t\tstopTokenIndex: number, // inclusive\r\n\t): ParserRuleContext | undefined {\r\n\t\tlet n: number = t.childCount;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tlet child: ParseTree = t.getChild(i);\r\n\t\t\tlet r = Trees.getRootOfSubtreeEnclosingRegion(child, startTokenIndex, stopTokenIndex);\r\n\t\t\tif (r) {\r\n\t\t\t\treturn r;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (t instanceof ParserRuleContext) {\r\n\t\t\tlet stopToken = t.stop;\r\n\t\t\tif (startTokenIndex >= t.start.tokenIndex && // is range fully contained in t?\r\n\t\t\t\t(stopToken == null || stopTokenIndex <= stopToken.tokenIndex)) {\r\n\t\t\t\t// note: r.stop==null likely implies that we bailed out of parser and there's nothing to the right\r\n\t\t\t\treturn t;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/** Replace any subtree siblings of root that are completely to left\r\n\t *  or right of lookahead range with a CommonToken(Token.INVALID_TYPE,\"...\")\r\n\t *  node. The source interval for t is not altered to suit smaller range!\r\n\t *\r\n\t *  WARNING: destructive to t.\r\n\t *\r\n\t *  @since 4.5.1\r\n\t */\r\n\tpublic static stripChildrenOutOfRange(\r\n\t\tt: ParserRuleContext,\r\n\t\troot: ParserRuleContext,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number): void {\r\n\t\tif (!t) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tlet count = t.childCount;\r\n\t\tfor (let i = 0; i < count; i++) {\r\n\t\t\tlet child = t.getChild(i);\r\n\t\t\tlet range: Interval = child.sourceInterval;\r\n\t\t\tif (child instanceof ParserRuleContext && (range.b < startIndex || range.a > stopIndex)) {\r\n\t\t\t\tif (Trees.isAncestorOf(child, root)) { // replace only if subtree doesn't have displayed root\r\n\t\t\t\t\tlet abbrev: CommonToken = new CommonToken(Token.INVALID_TYPE, \"...\");\r\n\t\t\t\t\tt.children![i] = new TerminalNode(abbrev); // HACK access to private\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/** Return first node satisfying the pred\r\n\t *\r\n\t *  @since 4.5.1\r\n\t */\r\n\tpublic static findNodeSuchThat(t: ParseTree, pred: (tree: ParseTree) => boolean): ParseTree | undefined;\r\n\tpublic static findNodeSuchThat(t: Tree, pred: (tree: Tree) => boolean): Tree | undefined;\r\n\tpublic static findNodeSuchThat(t: Tree, pred: (tree: ParseTree) => boolean): Tree | undefined {\r\n\t\t// No type check needed as long as users only use one of the available overloads\r\n\t\tif (pred(t as ParseTree)) {\r\n\t\t\treturn t;\r\n\t\t}\r\n\r\n\t\tlet n: number =  t.childCount;\r\n\t\tfor (let i = 0 ; i < n ; i++){\r\n\t\t\tlet u = Trees.findNodeSuchThat(t.getChild(i), pred as (tree: Tree) => boolean);\r\n\t\t\tif (u !== undefined) {\r\n\t\t\t\treturn u;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n}\r\n"]}