{"1": {"chainName": "Ethereum Mainnet", "nativeChainId": 1, "eid": "30101", "executor": "******************************************", "endpointV2": "******************************************", "sendUln301": "******************************************", "sendUln302": "******************************************", "receiveUln301": "******************************************", "receiveUln302": "******************************************"}, "421614": {"chainName": "Arbitrum Sepolia", "nativeChainId": 421614, "eid": "40231", "executor": "******************************************", "endpointV2": "******************************************", "sendUln301": "******************************************", "sendUln302": "******************************************", "receiveUln301": "******************************************", "receiveUln302": "******************************************"}, "11155111": {"chainName": "Ethereum <PERSON>", "nativeChainId": 11155111, "eid": "40161", "executor": "******************************************", "endpointV2": "******************************************", "sendUln301": "******************************************", "sendUln302": "******************************************", "receiveUln301": "******************************************", "receiveUln302": "******************************************"}, "17000": {"chainName": "Ethereum Holesky Testnet", "nativeChainId": 17000, "eid": "40217", "executor": "******************************************", "endpointV2": "******************************************", "sendUln301": "******************************************", "sendUln302": "******************************************", "receiveUln301": "******************************************", "receiveUln302": "******************************************"}, "6342": {"chainName": "MegaETH Testnet", "nativeChainId": 6342, "eid": "40370", "executor": "******************************************", "endpointV2": "******************************************", "sendUln301": "******************************************", "sendUln302": "******************************************", "receiveUln301": "******************************************", "receiveUln302": "******************************************"}}