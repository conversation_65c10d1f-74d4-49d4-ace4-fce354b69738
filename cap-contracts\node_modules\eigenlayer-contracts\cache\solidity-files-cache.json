{"_format": "", "paths": {"artifacts": "out", "build_infos": "out/build-info", "sources": "src", "tests": "test", "scripts": "script", "libraries": ["lib"]}, "files": {"lib/ds-test/src/test.sol": {"lastModificationDate": 1730737140300, "contentHash": "9febff9d09f18af5306669dc276c4c43", "sourceName": "lib/ds-test/src/test.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"DSTest": {"0.8.12": {"path": "test.sol/DSTest.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/Base.sol": {"lastModificationDate": 1730737140346, "contentHash": "8f04bbbb2c16f79e14fdc321695a8ec2", "sourceName": "lib/forge-std/src/Base.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"CommonBase": {"0.8.12": {"path": "Base.sol/CommonBase.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "ScriptBase": {"0.8.12": {"path": "Base.sol/ScriptBase.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "TestBase": {"0.8.12": {"path": "Base.sol/TestBase.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/Script.sol": {"lastModificationDate": 1730737140346, "contentHash": "83c39354c1e43190bce4dc43860dc786", "sourceName": "lib/forge-std/src/Script.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Script": {"0.8.12": {"path": "Script.sol/Script.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdAssertions.sol": {"lastModificationDate": 1730737140346, "contentHash": "6cc2858240bcd443debbbf075490e325", "sourceName": "lib/forge-std/src/StdAssertions.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/StdMath.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdAssertions": {"0.8.12": {"path": "StdAssertions.sol/StdAssertions.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdChains.sol": {"lastModificationDate": 1730737140346, "contentHash": "94ba68ea5c34509e1355d69df27597ce", "sourceName": "lib/forge-std/src/StdChains.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdChains": {"0.8.12": {"path": "StdChains.sol/StdChains.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdCheats.sol": {"lastModificationDate": 1730737140347, "contentHash": "1f7c1fe2f20a051b847336e1630e4bb5", "sourceName": "lib/forge-std/src/StdCheats.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdCheats": {"0.8.12": {"path": "StdCheats.sol/StdCheats.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StdCheatsSafe": {"0.8.12": {"path": "StdCheats.sol/StdCheatsSafe.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdError.sol": {"lastModificationDate": 1730737140347, "contentHash": "64c896e1276a291776e5ea5aecb3870a", "sourceName": "lib/forge-std/src/StdError.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdError": {"0.8.12": {"path": "StdError.sol/stdError.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdInvariant.sol": {"lastModificationDate": 1730737140347, "contentHash": "12c06010ec43ce935ed209d5aca30828", "sourceName": "lib/forge-std/src/StdInvariant.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdInvariant": {"0.8.12": {"path": "StdInvariant.sol/StdInvariant.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdJson.sol": {"lastModificationDate": 1730737140347, "contentHash": "2e1d13674e152408867795362d833c24", "sourceName": "lib/forge-std/src/StdJson.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdJson": {"0.8.12": {"path": "StdJson.sol/stdJson.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdMath.sol": {"lastModificationDate": 1730737140347, "contentHash": "9da8f453eba6bb98f3d75bc6822bfb29", "sourceName": "lib/forge-std/src/StdMath.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdMath": {"0.8.12": {"path": "StdMath.sol/stdMath.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStorage.sol": {"lastModificationDate": 1730737140347, "contentHash": "3cb9868082df39a53927db09dbc21f23", "sourceName": "lib/forge-std/src/StdStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdStorage": {"0.8.12": {"path": "StdStorage.sol/stdStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "stdStorageSafe": {"0.8.12": {"path": "StdStorage.sol/stdStorageSafe.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStyle.sol": {"lastModificationDate": 1730737140347, "contentHash": "820e6f6b7704f0c980fae604b1e34b18", "sourceName": "lib/forge-std/src/StdStyle.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"StdStyle": {"0.8.12": {"path": "StdStyle.sol/StdStyle.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdUtils.sol": {"lastModificationDate": 1730737140347, "contentHash": "d5ff36b0cdb1b976dd77ae53fe96d0c6", "sourceName": "lib/forge-std/src/StdUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdUtils": {"0.8.12": {"path": "StdUtils.sol/StdUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/Test.sol": {"lastModificationDate": 1730737140348, "contentHash": "bc9d74a0c612e1c2b66cca2973bcc803", "sourceName": "lib/forge-std/src/Test.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Test": {"0.8.12": {"path": "Test.sol/Test.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/Vm.sol": {"lastModificationDate": 1730737140348, "contentHash": "68cca88e2df06231acff7abb1f6bdea6", "sourceName": "lib/forge-std/src/Vm.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Vm": {"0.8.12": {"path": "Vm.sol/Vm.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "VmSafe": {"0.8.12": {"path": "Vm.sol/VmSafe.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/console.sol": {"lastModificationDate": 1730737140348, "contentHash": "100b8a33b917da1147740d7ab8b0ded3", "sourceName": "lib/forge-std/src/console.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console": {"0.8.12": {"path": "console.sol/console.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/console2.sol": {"lastModificationDate": 1730737140348, "contentHash": "2096b4e5f252c5df9909cccbe3d2da2e", "sourceName": "lib/forge-std/src/console2.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console2": {"0.8.12": {"path": "console2.sol/console2.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"lastModificationDate": 1730737140349, "contentHash": "7b131ca1ca32ef6378b7b9ad5488b901", "sourceName": "lib/forge-std/src/interfaces/IMulticall3.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IMulticall3": {"0.8.12": {"path": "IMulticall3.sol/IMulticall3.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"lastModificationDate": 1730737141861, "contentHash": "e436cea06129be2c73cda4b1acc848b5", "sourceName": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"Ownable": {"0.8.12": {"path": "Ownable.sol/Ownable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol": {"lastModificationDate": 1730737141870, "contentHash": "8fe867b95c856b204f954a1910e28a1e", "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1271": {"0.8.12": {"path": "IERC1271.sol/IERC1271.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"lastModificationDate": 1730737141872, "contentHash": "d151fbfe7939989b9acf22797b32058b", "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol": {"lastModificationDate": 1730737141874, "contentHash": "2858d98e74e67987ec81b39605230b74", "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1822Proxiable": {"0.8.12": {"path": "draft-IERC1822.sol/IERC1822Proxiable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol": {"lastModificationDate": 1730737141880, "contentHash": "918863d96faa1e17cd245dfaf23ac786", "sourceName": "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC1271MaliciousMock": {"0.8.12": {"path": "ERC1271WalletMock.sol/ERC1271MaliciousMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "ERC1271WalletMock": {"0.8.12": {"path": "ERC1271WalletMock.sol/ERC1271WalletMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"lastModificationDate": 1730737141895, "contentHash": "3fc3c7c0a2956f36e766691bb9473b06", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC1967Proxy": {"0.8.12": {"path": "ERC1967Proxy.sol/ERC1967Proxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol": {"lastModificationDate": 1730737141895, "contentHash": "6baa887a798e95b14f34e093f117e9b2", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.2", "artifacts": {"ERC1967Upgrade": {"0.8.12": {"path": "ERC1967Upgrade.sol/ERC1967Upgrade.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"lastModificationDate": 1730737141896, "contentHash": "40b3d81a836d50ff47e03893dcaaf204", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Proxy": {"0.8.12": {"path": "Proxy.sol/Proxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol": {"lastModificationDate": 1730737141896, "contentHash": "dc9dcb6e542154d9cfbfaece646c1092", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BeaconProxy": {"0.8.12": {"path": "BeaconProxy.sol/BeaconProxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"lastModificationDate": 1730737141897, "contentHash": "b6bd23bf19e90b771337037706470933", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IBeacon": {"0.8.12": {"path": "IBeacon.sol/IBeacon.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol": {"lastModificationDate": 1730737141897, "contentHash": "8ffefb755605824cf730ce4092b2f581", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"UpgradeableBeacon": {"0.8.12": {"path": "UpgradeableBeacon.sol/UpgradeableBeacon.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"lastModificationDate": 1730737141897, "contentHash": "a947492251ac15d6bfd899c9fdb4d82b", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ProxyAdmin": {"0.8.12": {"path": "ProxyAdmin.sol/ProxyAdmin.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"lastModificationDate": 1730737141898, "contentHash": "ea48b4a63fd733eec048191be006daa8", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"TransparentUpgradeableProxy": {"0.8.12": {"path": "TransparentUpgradeableProxy.sol/TransparentUpgradeableProxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1730737141903, "contentHash": "af7bd64e1cfefbf6cb07f2adc1a25392", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20": {"0.8.12": {"path": "ERC20.sol/ERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1730737141904, "contentHash": "ad7c2d0af148c8f9f097d65deeb4da6b", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20": {"0.8.12": {"path": "IERC20.sol/IERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"lastModificationDate": 1730737141904, "contentHash": "a1c7f80ae26f5b2d7d563475627fbf25", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20Burnable": {"0.8.12": {"path": "ERC20Burnable.sol/ERC20Burnable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1730737141905, "contentHash": "909ab67fc5c25033fe6cd364f8c056f9", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Metadata": {"0.8.12": {"path": "IERC20Metadata.sol/IERC20Metadata.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol": {"lastModificationDate": 1730737141906, "contentHash": "fb77f144244b9ab12533aa6ce85ef8c5", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Permit": {"0.8.12": {"path": "draft-IERC20Permit.sol/IERC20Permit.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol": {"lastModificationDate": 1730737141906, "contentHash": "d4d35c0977f3c87ab399a75a45d6fc19", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20PresetFixedSupply": {"0.8.12": {"path": "ERC20PresetFixedSupply.sol/ERC20PresetFixedSupply.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"lastModificationDate": 1730737141907, "contentHash": "3a843b05b85a270e9455e3d2e804e633", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol"], "versionRequirement": "^0.8.0", "artifacts": {"SafeERC20": {"0.8.12": {"path": "SafeERC20.sol/SafeERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"lastModificationDate": 1730737141913, "contentHash": "c476b3895a94798b88a4bb97399e6dfe", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.1", "artifacts": {"Address": {"0.8.12": {"path": "Address.sol/Address.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"lastModificationDate": 1730737141913, "contentHash": "5f2c5c4b6af2dd4551027144797bc8be", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Context": {"0.8.12": {"path": "Context.sol/Context.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Create2.sol": {"lastModificationDate": 1730737141914, "contentHash": "8932e2855e9aac6e79af79e499863b10", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Create2": {"0.8.12": {"path": "Create2.sol/Create2.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1730737141914, "contentHash": "f993f8f50186952a59ee5e3a30b68222", "sourceName": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"StorageSlot": {"0.8.12": {"path": "StorageSlot.sol/StorageSlot.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"lastModificationDate": 1730737141915, "contentHash": "cf46906c4035f51639a22265066a9e78", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Strings": {"0.8.12": {"path": "Strings.sol/Strings.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1730737141915, "contentHash": "1dfb7cf7c7e2edae73403d50a59cc967", "sourceName": "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Strings.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ECDSA": {"0.8.12": {"path": "ECDSA.sol/ECDSA.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"lastModificationDate": 1730737142031, "contentHash": "403ce8273abde646bff81558ddf512ad", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"OwnableUpgradeable": {"0.8.12": {"path": "OwnableUpgradeable.sol/OwnableUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"lastModificationDate": 1730737142062, "contentHash": "b98e2f3a856e6e7f2106fb919bacab9e", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.2", "artifacts": {"Initializable": {"0.8.12": {"path": "Initializable.sol/Initializable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol": {"lastModificationDate": 1730737142062, "contentHash": "c9cde6037fc8b1fe0ef04b79149ba733", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ReentrancyGuardUpgradeable": {"0.8.12": {"path": "ReentrancyGuardUpgradeable.sol/ReentrancyGuardUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol": {"lastModificationDate": 1730737142071, "contentHash": "d42e87f4fba2b03ab4d3c14cb53d0c51", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.1", "artifacts": {"AddressUpgradeable": {"0.8.12": {"path": "AddressUpgradeable.sol/AddressUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"lastModificationDate": 1730737142072, "contentHash": "6200b84950eb05b4a92a39fd1d6e0f9b", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ContextUpgradeable": {"0.8.12": {"path": "ContextUpgradeable.sol/ContextUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol": {"lastModificationDate": 1730737142193, "contentHash": "db24b956904575cc79d02867ec03263c", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"OwnableUpgradeable": {"0.8.12": {"path": "access/OwnableUpgradeable.sol/OwnableUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol": {"lastModificationDate": 1730737142200, "contentHash": "80bff8876cb4092b3586097e1d0c16ab", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IVotesUpgradeable": {"0.8.12": {"path": "IVotesUpgradeable.sol/IVotesUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol": {"lastModificationDate": 1730737142203, "contentHash": "2b3f03127c03dd67be61c925b1f86ff3", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC5267Upgradeable": {"0.8.12": {"path": "IERC5267Upgradeable.sol/IERC5267Upgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol": {"lastModificationDate": 1730737142203, "contentHash": "c3a9d2a1ed12ceba679f14eb6d3a57e5", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"IERC5805Upgradeable": {"0.8.12": {"path": "IERC5805Upgradeable.sol/IERC5805Upgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol": {"lastModificationDate": 1730737142204, "contentHash": "e2c2c53276d18d51c28f29f529e1eae8", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC6372Upgradeable": {"0.8.12": {"path": "IERC6372Upgradeable.sol/IERC6372Upgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol": {"lastModificationDate": 1730737142219, "contentHash": "b0970a564d121abf9adfff8d1a01eb16", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.2", "artifacts": {"Initializable": {"0.8.12": {"path": "utils/Initializable.sol/Initializable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol": {"lastModificationDate": 1730737142223, "contentHash": "569edd57f8c6c7f5f750a3da1ed9c4c7", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20Upgradeable": {"0.8.12": {"path": "ERC20Upgradeable.sol/ERC20Upgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol": {"lastModificationDate": 1730737142223, "contentHash": "9a9398a7dbda9d014f04d5eb0fb581fd", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Upgradeable": {"0.8.12": {"path": "IERC20Upgradeable.sol/IERC20Upgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol": {"lastModificationDate": 1730737142224, "contentHash": "ad0a0db40897f20f710e8cd4583ffdb8", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20PermitUpgradeable": {"0.8.12": {"path": "ERC20PermitUpgradeable.sol/ERC20PermitUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol": {"lastModificationDate": 1730737142225, "contentHash": "d0a956d9adf315b060b8a25c2551562f", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20VotesUpgradeable": {"0.8.12": {"path": "ERC20VotesUpgradeable.sol/ERC20VotesUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol": {"lastModificationDate": 1730737142225, "contentHash": "9efcd5467a7f0bf533910ee8a267adb2", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"IERC20MetadataUpgradeable": {"0.8.12": {"path": "IERC20MetadataUpgradeable.sol/IERC20MetadataUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol": {"lastModificationDate": 1730737142225, "contentHash": "30ef13e9a1b22e74e5e4ce5ab2a247e4", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20PermitUpgradeable": {"0.8.12": {"path": "IERC20PermitUpgradeable.sol/IERC20PermitUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol": {"lastModificationDate": 1730737142232, "contentHash": "c30c805386fda8a42ff515da963d3a95", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.1", "artifacts": {"AddressUpgradeable": {"0.8.12": {"path": "utils/AddressUpgradeable.sol/AddressUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol": {"lastModificationDate": 1730737142233, "contentHash": "6200b84950eb05b4a92a39fd1d6e0f9b", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ContextUpgradeable": {"0.8.12": {"path": "utils/ContextUpgradeable.sol/ContextUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol": {"lastModificationDate": 1730737142233, "contentHash": "2e908c762a799baea365e68a50500e2c", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"CountersUpgradeable": {"0.8.12": {"path": "CountersUpgradeable.sol/CountersUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol": {"lastModificationDate": 1730737142234, "contentHash": "c221361be1c4953f5b71f47475b90266", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"StringsUpgradeable": {"0.8.12": {"path": "StringsUpgradeable.sol/StringsUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol": {"lastModificationDate": 1730737142234, "contentHash": "e6a08d062a6f4f6b05822a0fa19a1606", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ECDSAUpgradeable": {"0.8.12": {"path": "ECDSAUpgradeable.sol/ECDSAUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol": {"lastModificationDate": 1730737142235, "contentHash": "1574f8139cf0b307447c86cc89cd08f8", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol"], "versionRequirement": "^0.8.8", "artifacts": {"EIP712Upgradeable": {"0.8.12": {"path": "EIP712Upgradeable.sol/EIP712Upgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol": {"lastModificationDate": 1730737142237, "contentHash": "5a2a749b45b6a8eb035f4bf75addcb27", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"MathUpgradeable": {"0.8.12": {"path": "MathUpgradeable.sol/MathUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol": {"lastModificationDate": 1730737142237, "contentHash": "8565f9d83dfbed34d9ff896c02e23ae7", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"SafeCastUpgradeable": {"0.8.12": {"path": "SafeCastUpgradeable.sol/SafeCastUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol": {"lastModificationDate": 1730737142238, "contentHash": "2a6b819b2e241091ada6d645df3e3929", "sourceName": "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"SignedMathUpgradeable": {"0.8.12": {"path": "SignedMathUpgradeable.sol/SignedMathUpgradeable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/access/Ownable.sol": {"lastModificationDate": 1730737147112, "contentHash": "5a20b2cad87ddb61c7a3a6af21289e28", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/access/Ownable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"Ownable": {"0.8.12": {"path": "access/Ownable.sol/Ownable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol": {"lastModificationDate": 1730737147124, "contentHash": "d0d060231a45da7a1eecbb5cd286fa40", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1967": {"0.8.12": {"path": "IERC1967.sol/IERC1967.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol": {"lastModificationDate": 1730737147127, "contentHash": "2858d98e74e67987ec81b39605230b74", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1822Proxiable": {"0.8.12": {"path": "interfaces/draft-IERC1822.sol/IERC1822Proxiable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"lastModificationDate": 1730737147139, "contentHash": "3fc3c7c0a2956f36e766691bb9473b06", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Proxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC1967Proxy": {"0.8.12": {"path": "ERC1967/ERC1967Proxy.sol/ERC1967Proxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Upgrade.sol": {"lastModificationDate": 1730737147139, "contentHash": "a127706394bead18392601a20d44867a", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.2", "artifacts": {"ERC1967Upgrade": {"0.8.12": {"path": "ERC1967/ERC1967Upgrade.sol/ERC1967Upgrade.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/Proxy.sol": {"lastModificationDate": 1730737147139, "contentHash": "40b3d81a836d50ff47e03893dcaaf204", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/Proxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Proxy": {"0.8.12": {"path": "proxy/Proxy.sol/Proxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol": {"lastModificationDate": 1730737147139, "contentHash": "b6bd23bf19e90b771337037706470933", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IBeacon": {"0.8.12": {"path": "beacon/IBeacon.sol/IBeacon.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/ProxyAdmin.sol": {"lastModificationDate": 1730737147140, "contentHash": "9891986e27d357222a8ac8c0c13abe31", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/ProxyAdmin.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/access/Ownable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ProxyAdmin": {"0.8.12": {"path": "transparent/ProxyAdmin.sol/ProxyAdmin.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"lastModificationDate": 1730737147140, "contentHash": "1286aa8d056c7120d1a2da252e310d2f", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ITransparentUpgradeableProxy": {"0.8.12": {"path": "TransparentUpgradeableProxy.sol/ITransparentUpgradeableProxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "TransparentUpgradeableProxy": {"0.8.12": {"path": "transparent/TransparentUpgradeableProxy.sol/TransparentUpgradeableProxy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1730737147144, "contentHash": "********************************", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/ERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20": {"0.8.12": {"path": "ERC20/ERC20.sol/ERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1730737147144, "contentHash": "df36f7051335cd1e748b1b6463b7fdd3", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20": {"0.8.12": {"path": "ERC20/IERC20.sol/IERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"lastModificationDate": 1730737147144, "contentHash": "a1c7f80ae26f5b2d7d563475627fbf25", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/ERC20Burnable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20Burnable": {"0.8.12": {"path": "extensions/ERC20Burnable.sol/ERC20Burnable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1730737147146, "contentHash": "909ab67fc5c25033fe6cd364f8c056f9", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/IERC20Metadata.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Metadata": {"0.8.12": {"path": "extensions/IERC20Metadata.sol/IERC20Metadata.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol": {"lastModificationDate": 1730737147147, "contentHash": "7189d4fb9e7caab0ab619a13dc996930", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20PresetFixedSupply": {"0.8.12": {"path": "presets/ERC20PresetFixedSupply.sol/ERC20PresetFixedSupply.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol": {"lastModificationDate": 1730737147152, "contentHash": "211ffd288c1588ba8c10eae668ca3c66", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.1", "artifacts": {"Address": {"0.8.12": {"path": "utils/Address.sol/Address.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol": {"lastModificationDate": 1730737147153, "contentHash": "5f2c5c4b6af2dd4551027144797bc8be", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Context": {"0.8.12": {"path": "utils/Context.sol/Context.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1730737147155, "contentHash": "682f7dd1f2e1147c8390e7575deceb2d", "sourceName": "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"StorageSlot": {"0.8.12": {"path": "utils/StorageSlot.sol/StorageSlot.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/interfaces/IMultiSend.sol": {"lastModificationDate": 1730737151172, "contentHash": "02468c4aa70cc2542e45e9dfdfa1a988", "sourceName": "lib/zeus-templates/src/interfaces/IMultiSend.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IMultiSend": {"0.8.12": {"path": "IMultiSend.sol/IMultiSend.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/interfaces/ISafe.sol": {"lastModificationDate": 1730737151173, "contentHash": "8a27da9d91709da6dd7f8abd6633a72a", "sourceName": "lib/zeus-templates/src/interfaces/ISafe.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"ISafe": {"0.8.12": {"path": "ISafe.sol/ISafe.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/interfaces/ITimelock.sol": {"lastModificationDate": 1730737151173, "contentHash": "66d637edc9ee422377d0834efcc0e963", "sourceName": "lib/zeus-templates/src/interfaces/ITimelock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"ITimelock": {"0.8.12": {"path": "ITimelock.sol/ITimelock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/templates/EOADeployer.sol": {"lastModificationDate": 1730737151173, "contentHash": "347428609d093e4a8cabceccbe91466e", "sourceName": "lib/zeus-templates/src/templates/EOADeployer.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EOADeployer": {"0.8.12": {"path": "EOADeployer.sol/EOADeployer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/templates/MultisigBuilder.sol": {"lastModificationDate": 1730737151173, "contentHash": "c268e83a71b1004250313077b341e0a5", "sourceName": "lib/zeus-templates/src/templates/MultisigBuilder.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/zeus-templates/src/interfaces/IMultiSend.sol", "lib/zeus-templates/src/interfaces/ISafe.sol", "lib/zeus-templates/src/utils/EncGnosisSafe.sol", "lib/zeus-templates/src/utils/MultisigCallUtils.sol", "lib/zeus-templates/src/utils/SafeTxUtils.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol"], "versionRequirement": "^0.8.12", "artifacts": {"MultisigBuilder": {"0.8.12": {"path": "MultisigBuilder.sol/MultisigBuilder.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/utils/EncGnosisSafe.sol": {"lastModificationDate": 1730737151173, "contentHash": "e8fd5fbf53d46b1efa0af7663a0f4d25", "sourceName": "lib/zeus-templates/src/utils/EncGnosisSafe.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/zeus-templates/src/interfaces/ISafe.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EncGnosisSafe": {"0.8.12": {"path": "EncGnosisSafe.sol/EncGnosisSafe.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/utils/MultisigCallUtils.sol": {"lastModificationDate": 1730737151173, "contentHash": "01ef5c7ba1646ee69b057647b4bd26a4", "sourceName": "lib/zeus-templates/src/utils/MultisigCallUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/zeus-templates/src/interfaces/IMultiSend.sol", "lib/zeus-templates/src/interfaces/ISafe.sol"], "versionRequirement": "^0.8.12", "artifacts": {"MultisigCallUtils": {"0.8.12": {"path": "MultisigCallUtils.sol/MultisigCallUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/utils/SafeTxUtils.sol": {"lastModificationDate": 1730737151173, "contentHash": "f47309a82bd4c384fb458625ddaf116d", "sourceName": "lib/zeus-templates/src/utils/SafeTxUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/zeus-templates/src/interfaces/ISafe.sol", "lib/zeus-templates/src/utils/EncGnosisSafe.sol"], "versionRequirement": "^0.8.12", "artifacts": {"SafeTxUtils": {"0.8.12": {"path": "SafeTxUtils.sol/SafeTxUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/utils/StringUtils.sol": {"lastModificationDate": 1730737151174, "contentHash": "dd2e0d5cf239255e103b558ee3f50e69", "sourceName": "lib/zeus-templates/src/utils/StringUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"StringUtils": {"0.8.12": {"path": "StringUtils.sol/StringUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "lib/zeus-templates/src/utils/ZeusScript.sol": {"lastModificationDate": 1730737151174, "contentHash": "af787bdadeaf4f916c8b443d32348c4e", "sourceName": "lib/zeus-templates/src/utils/ZeusScript.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/zeus-templates/src/utils/StringUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ZeusScript": {"0.8.12": {"path": "ZeusScript.sol/ZeusScript.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/deploy/local/Deploy_From_Scratch.s.sol": {"lastModificationDate": 1730704250565, "contentHash": "5c9c5c5d572f91bbdcfa47a7a0fd4292", "sourceName": "script/deploy/local/Deploy_From_Scratch.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DeployFromScratch": {"0.8.12": {"path": "Deploy_From_Scratch.s.sol/DeployFromScratch.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/interfaces/IUpgradeableBeacon.sol": {"lastModificationDate": 1730704250565, "contentHash": "941a59395bc86cd188b2633b84caa599", "sourceName": "script/interfaces/IUpgradeableBeacon.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IUpgradeableBeacon": {"0.8.12": {"path": "IUpgradeableBeacon.sol/IUpgradeableBeacon.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/releases/release-template/1-eoa.s.sol": {"lastModificationDate": 1730704250566, "contentHash": "********************************", "sourceName": "script/releases/release-template/1-eoa.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/zeus-templates/src/templates/EOADeployer.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Deploy": {"0.8.12": {"path": "1-eoa.s.sol/Deploy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/releases/release-template/2-multisig.s.sol": {"lastModificationDate": 1730704250566, "contentHash": "7da445d1bc1a597de043f3ba44fa641f", "sourceName": "script/releases/release-template/2-multisig.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/zeus-templates/src/interfaces/IMultiSend.sol", "lib/zeus-templates/src/interfaces/ISafe.sol", "lib/zeus-templates/src/interfaces/ITimelock.sol", "lib/zeus-templates/src/templates/MultisigBuilder.sol", "lib/zeus-templates/src/utils/EncGnosisSafe.sol", "lib/zeus-templates/src/utils/MultisigCallUtils.sol", "lib/zeus-templates/src/utils/SafeTxUtils.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Queue": {"0.8.12": {"path": "2-multisig.s.sol/Queue.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/releases/release-template/3-multisig.s.sol": {"lastModificationDate": 1730704250566, "contentHash": "3d0f81b684ee9151a6fcfbd92191e575", "sourceName": "script/releases/release-template/3-multisig.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/zeus-templates/src/interfaces/IMultiSend.sol", "lib/zeus-templates/src/interfaces/ISafe.sol", "lib/zeus-templates/src/interfaces/ITimelock.sol", "lib/zeus-templates/src/templates/MultisigBuilder.sol", "lib/zeus-templates/src/utils/EncGnosisSafe.sol", "lib/zeus-templates/src/utils/MultisigCallUtils.sol", "lib/zeus-templates/src/utils/SafeTxUtils.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol", "script/releases/release-template/2-multisig.s.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Execute": {"0.8.12": {"path": "3-multisig.s.sol/Execute.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/releases/v0.1-eigenpod-example/1-eoa.s.sol": {"lastModificationDate": 1730704250566, "contentHash": "068bdaf470131078abd112f4b859cbd6", "sourceName": "script/releases/v0.1-eigenpod-example/1-eoa.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/zeus-templates/src/templates/EOADeployer.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DeployEigenPodAndManager": {"0.8.12": {"path": "1-eoa.s.sol/DeployEigenPodAndManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/releases/v0.1-eigenpod-example/2-multisig.s.sol": {"lastModificationDate": 1730704250566, "contentHash": "a86c2253b661466c1712deba818c0797", "sourceName": "script/releases/v0.1-eigenpod-example/2-multisig.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/zeus-templates/src/interfaces/IMultiSend.sol", "lib/zeus-templates/src/interfaces/ISafe.sol", "lib/zeus-templates/src/interfaces/ITimelock.sol", "lib/zeus-templates/src/templates/MultisigBuilder.sol", "lib/zeus-templates/src/utils/EncGnosisSafe.sol", "lib/zeus-templates/src/utils/MultisigCallUtils.sol", "lib/zeus-templates/src/utils/SafeTxUtils.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol", "script/interfaces/IUpgradeableBeacon.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol"], "versionRequirement": "^0.8.12", "artifacts": {"QueueEigenPodAndManager": {"0.8.12": {"path": "2-multisig.s.sol/QueueEigenPodAndManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/releases/v0.1-eigenpod-example/3-multisig.s.sol": {"lastModificationDate": 1730704250566, "contentHash": "9915b37515d55af8e4791707ec1e3be6", "sourceName": "script/releases/v0.1-eigenpod-example/3-multisig.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/zeus-templates/src/interfaces/IMultiSend.sol", "lib/zeus-templates/src/interfaces/ISafe.sol", "lib/zeus-templates/src/interfaces/ITimelock.sol", "lib/zeus-templates/src/templates/MultisigBuilder.sol", "lib/zeus-templates/src/utils/EncGnosisSafe.sol", "lib/zeus-templates/src/utils/MultisigCallUtils.sol", "lib/zeus-templates/src/utils/SafeTxUtils.sol", "lib/zeus-templates/src/utils/StringUtils.sol", "lib/zeus-templates/src/utils/ZeusScript.sol", "script/interfaces/IUpgradeableBeacon.sol", "script/releases/v0.1-eigenpod-example/2-multisig.s.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ExecuteEigenPodAndManager": {"0.8.12": {"path": "3-multisig.s.sol/ExecuteEigenPodAndManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "script/utils/ExistingDeploymentParser.sol": {"lastModificationDate": 1730704250567, "contentHash": "197e63bc9a18dae9c73425da6b5e8e0c", "sourceName": "script/utils/ExistingDeploymentParser.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ExistingDeploymentParser": {"0.8.12": {"path": "ExistingDeploymentParser.sol/ExistingDeploymentParser.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/AVSDirectory.sol": {"lastModificationDate": 1730736797024, "contentHash": "d6780c80501119af0b9ff2f54d508be2", "sourceName": "src/contracts/core/AVSDirectory.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"AVSDirectory": {"0.8.12": {"path": "AVSDirectory.sol/AVSDirectory.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/AVSDirectoryStorage.sol": {"lastModificationDate": 1730704250568, "contentHash": "8c1c4345d820d68403df95fee7c1d591", "sourceName": "src/contracts/core/AVSDirectoryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": "^0.8.12", "artifacts": {"AVSDirectoryStorage": {"0.8.12": {"path": "AVSDirectoryStorage.sol/AVSDirectoryStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/DelegationManager.sol": {"lastModificationDate": 1730736797182, "contentHash": "6d68255171bbb52f09b24dad0945117f", "sourceName": "src/contracts/core/DelegationManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationManager": {"0.8.12": {"path": "DelegationManager.sol/DelegationManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/DelegationManagerStorage.sol": {"lastModificationDate": 1730704250568, "contentHash": "a698eefc0931659fd047622da11f6c66", "sourceName": "src/contracts/core/DelegationManagerStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationManagerStorage": {"0.8.12": {"path": "DelegationManagerStorage.sol/DelegationManagerStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/RewardsCoordinator.sol": {"lastModificationDate": 1730736797186, "contentHash": "bdecd4834e61c873fd7b28a42bd3c4b9", "sourceName": "src/contracts/core/RewardsCoordinator.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RewardsCoordinator": {"0.8.12": {"path": "RewardsCoordinator.sol/RewardsCoordinator.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/RewardsCoordinatorStorage.sol": {"lastModificationDate": 1730704250568, "contentHash": "4f83e3142214e38c94f17ab27ae4397c", "sourceName": "src/contracts/core/RewardsCoordinatorStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RewardsCoordinatorStorage": {"0.8.12": {"path": "RewardsCoordinatorStorage.sol/RewardsCoordinatorStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/Slasher.sol": {"lastModificationDate": 1730736797091, "contentHash": "4af0fcef7a9ad9b52ae12f00384b95b7", "sourceName": "src/contracts/core/Slasher.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Slasher": {"0.8.12": {"path": "Slasher.sol/Slasher.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/StrategyManager.sol": {"lastModificationDate": 1730736797091, "contentHash": "991337eb5530bc48fc001c9308bb2c30", "sourceName": "src/contracts/core/StrategyManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManager": {"0.8.12": {"path": "StrategyManager.sol/StrategyManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/core/StrategyManagerStorage.sol": {"lastModificationDate": 1730704250569, "contentHash": "72c43df54fb523fe8084eed2288b8482", "sourceName": "src/contracts/core/StrategyManagerStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManagerStorage": {"0.8.12": {"path": "StrategyManagerStorage.sol/StrategyManagerStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IAVSDirectory.sol": {"lastModificationDate": 1730704250569, "contentHash": "d5b129d0f65915d6f261a0a4fa603446", "sourceName": "src/contracts/interfaces/IAVSDirectory.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/ISignatureUtils.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IAVSDirectory": {"0.8.12": {"path": "IAVSDirectory.sol/IAVSDirectory.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IBackingEigen.sol": {"lastModificationDate": 1730704250569, "contentHash": "dbbe324a5c8b3aa4e65e6e1c8f370a24", "sourceName": "src/contracts/interfaces/IBackingEigen.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IBackingEigen": {"0.8.12": {"path": "IBackingEigen.sol/IBackingEigen.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IDelegationFaucet.sol": {"lastModificationDate": 1730704250569, "contentHash": "ee5735a62970016d55d310df991aaad2", "sourceName": "src/contracts/interfaces/IDelegationFaucet.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IDelegationFaucet": {"0.8.12": {"path": "IDelegationFaucet.sol/IDelegationFaucet.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IDelegationManager.sol": {"lastModificationDate": 1730704250569, "contentHash": "1b9a7700899f112ad9c78b9db96f8980", "sourceName": "src/contracts/interfaces/IDelegationManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IDelegationManager": {"0.8.12": {"path": "IDelegationManager.sol/IDelegationManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IETHPOSDeposit.sol": {"lastModificationDate": 1730704250569, "contentHash": "4f6da7ff2685909ce76b5aff81db1b99", "sourceName": "src/contracts/interfaces/IETHPOSDeposit.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"IETHPOSDeposit": {"0.8.12": {"path": "IETHPOSDeposit.sol/IETHPOSDeposit.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IEigen.sol": {"lastModificationDate": 1730704250569, "contentHash": "1cf17e7d717f3e1d9de20373f7838200", "sourceName": "src/contracts/interfaces/IEigen.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IEigen": {"0.8.12": {"path": "IEigen.sol/IEigen.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IEigenPod.sol": {"lastModificationDate": 1730704250569, "contentHash": "5002ab0787a9bd1bc357e1fc133a1303", "sourceName": "src/contracts/interfaces/IEigenPod.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IEigenPod": {"0.8.12": {"path": "IEigenPod.sol/IEigenPod.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IEigenPodManager.sol": {"lastModificationDate": 1730704250570, "contentHash": "0fd6efd9b55ae9bb8dfde21b2d2018a1", "sourceName": "src/contracts/interfaces/IEigenPodManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IEigenPodManager": {"0.8.12": {"path": "IEigenPodManager.sol/IEigenPodManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IPausable.sol": {"lastModificationDate": 1730704250570, "contentHash": "81446a04df973d0aabba7c2178370a6c", "sourceName": "src/contracts/interfaces/IPausable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/IPauserRegistry.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IPausable": {"0.8.12": {"path": "IPausable.sol/IPausable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IPauserRegistry.sol": {"lastModificationDate": 1730704250570, "contentHash": "f8660f151eaa5ef574c8fe237bab126f", "sourceName": "src/contracts/interfaces/IPauserRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"IPauserRegistry": {"0.8.12": {"path": "IPauserRegistry.sol/IPauserRegistry.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IRewardsCoordinator.sol": {"lastModificationDate": 1730704250570, "contentHash": "696e0f760533c56ac415b2e428facd71", "sourceName": "src/contracts/interfaces/IRewardsCoordinator.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IRewardsCoordinator": {"0.8.12": {"path": "IRewardsCoordinator.sol/IRewardsCoordinator.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/ISignatureUtils.sol": {"lastModificationDate": 1730704250570, "contentHash": "89380251e8b83383f70e8ed6365c30df", "sourceName": "src/contracts/interfaces/ISignatureUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"ISignatureUtils": {"0.8.12": {"path": "ISignatureUtils.sol/ISignatureUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/ISlasher.sol": {"lastModificationDate": 1730704250570, "contentHash": "2420fa4011cf315bdb56b640fe0ba307", "sourceName": "src/contracts/interfaces/ISlasher.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"ISlasher": {"0.8.12": {"path": "ISlasher.sol/ISlasher.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/ISocketUpdater.sol": {"lastModificationDate": 1730704250571, "contentHash": "7e02d2fad2f7305813ee95a11a2a6d01", "sourceName": "src/contracts/interfaces/ISocketUpdater.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"ISocketUpdater": {"0.8.12": {"path": "ISocketUpdater.sol/ISocketUpdater.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IStrategy.sol": {"lastModificationDate": 1730704250571, "contentHash": "86a82749739c27d7baed6c44ef309c21", "sourceName": "src/contracts/interfaces/IStrategy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IStrategy": {"0.8.12": {"path": "IStrategy.sol/IStrategy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IStrategyFactory.sol": {"lastModificationDate": 1730704250571, "contentHash": "05d190a8f867bb3b656f119151226dc0", "sourceName": "src/contracts/interfaces/IStrategyFactory.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IStrategyFactory": {"0.8.12": {"path": "IStrategyFactory.sol/IStrategyFactory.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/interfaces/IStrategyManager.sol": {"lastModificationDate": 1730704250571, "contentHash": "a9f28fdbd73f6619fdb1e8806d34da24", "sourceName": "src/contracts/interfaces/IStrategyManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IStrategyManager": {"0.8.12": {"path": "IStrategyManager.sol/IStrategyManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/libraries/BeaconChainProofs.sol": {"lastModificationDate": 1730704250571, "contentHash": "fcf5ecadfeace6c39f7ca3d2a1e9c1d1", "sourceName": "src/contracts/libraries/BeaconChainProofs.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BeaconChainProofs": {"0.8.12": {"path": "BeaconChainProofs.sol/BeaconChainProofs.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/libraries/BytesLib.sol": {"lastModificationDate": 1730704250571, "contentHash": "4b108962dab16029195dde673c795b67", "sourceName": "src/contracts/libraries/BytesLib.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.8.0, <0.9.0", "artifacts": {"BytesLib": {"0.8.12": {"path": "BytesLib.sol/BytesLib.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/libraries/EIP1271SignatureUtils.sol": {"lastModificationDate": 1730704250571, "contentHash": "3517cb096b4a96beb140462b69e61027", "sourceName": "src/contracts/libraries/EIP1271SignatureUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EIP1271SignatureUtils": {"0.8.12": {"path": "EIP1271SignatureUtils.sol/EIP1271SignatureUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/libraries/Endian.sol": {"lastModificationDate": 1730704250571, "contentHash": "6423d2c9344d84c22f4e347a973ba7b5", "sourceName": "src/contracts/libraries/Endian.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Endian": {"0.8.12": {"path": "Endian.sol/Endian.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/libraries/Merkle.sol": {"lastModificationDate": 1730704250572, "contentHash": "f81f144f4f61239fdb4867215cc95155", "sourceName": "src/contracts/libraries/Merkle.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Merkle": {"0.8.12": {"path": "Merkle.sol/Merkle.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/libraries/StructuredLinkedList.sol": {"lastModificationDate": 1730704250572, "contentHash": "739bae0ef39f55db5f719b2a25e919f4", "sourceName": "src/contracts/libraries/StructuredLinkedList.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"StructuredLinkedList": {"0.8.12": {"path": "StructuredLinkedList.sol/StructuredLinkedList.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/permissions/Pausable.sol": {"lastModificationDate": 1730704250572, "contentHash": "9673586545a7a38943b5ca981060f9ae", "sourceName": "src/contracts/permissions/Pausable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Pausable": {"0.8.12": {"path": "Pausable.sol/Pausable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/permissions/PauserRegistry.sol": {"lastModificationDate": 1730704250572, "contentHash": "1beba8d31e76b28a6c56f78007e41e2f", "sourceName": "src/contracts/permissions/PauserRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/IPauserRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PauserRegistry": {"0.8.12": {"path": "PauserRegistry.sol/PauserRegistry.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/pods/EigenPod.sol": {"lastModificationDate": 1730736796892, "contentHash": "fdca28153b1fc914f07d2fb1c2c72a1e", "sourceName": "src/contracts/pods/EigenPod.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPod": {"0.8.12": {"path": "EigenPod.sol/EigenPod.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/pods/EigenPodManager.sol": {"lastModificationDate": 1730736796912, "contentHash": "220be00fcc70bf8d95271c203d9293f8", "sourceName": "src/contracts/pods/EigenPodManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodManager": {"0.8.12": {"path": "EigenPodManager.sol/EigenPodManager.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/pods/EigenPodManagerStorage.sol": {"lastModificationDate": 1730704250573, "contentHash": "bd070141ceda52c6221ea1b8bdebcdfb", "sourceName": "src/contracts/pods/EigenPodManagerStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodManagerStorage": {"0.8.12": {"path": "EigenPodManagerStorage.sol/EigenPodManagerStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/pods/EigenPodPausingConstants.sol": {"lastModificationDate": 1730704250573, "contentHash": "2f49a00ca803d0e7e17816dde4843f42", "sourceName": "src/contracts/pods/EigenPodPausingConstants.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodPausingConstants": {"0.8.12": {"path": "EigenPodPausingConstants.sol/EigenPodPausingConstants.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/pods/EigenPodStorage.sol": {"lastModificationDate": 1730704250573, "contentHash": "4c5358be3bd3a1eb3ec165cead077e62", "sourceName": "src/contracts/pods/EigenPodStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodStorage": {"0.8.12": {"path": "EigenPodStorage.sol/EigenPodStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/strategies/EigenStrategy.sol": {"lastModificationDate": 1730704250573, "contentHash": "d423d9efac6441c9ccff7bdf38068516", "sourceName": "src/contracts/strategies/EigenStrategy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/strategies/StrategyBase.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenStrategy": {"0.8.12": {"path": "EigenStrategy.sol/EigenStrategy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/strategies/StrategyBase.sol": {"lastModificationDate": 1730736797198, "contentHash": "16d0b84a4187c26422ccc4109a503906", "sourceName": "src/contracts/strategies/StrategyBase.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyBase": {"0.8.12": {"path": "StrategyBase.sol/StrategyBase.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/strategies/StrategyBaseTVLLimits.sol": {"lastModificationDate": 1730704250574, "contentHash": "c920d1376495e0eccd813696434a5f11", "sourceName": "src/contracts/strategies/StrategyBaseTVLLimits.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/strategies/StrategyBase.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyBaseTVLLimits": {"0.8.12": {"path": "StrategyBaseTVLLimits.sol/StrategyBaseTVLLimits.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/strategies/StrategyFactory.sol": {"lastModificationDate": 1730736797194, "contentHash": "2f01115c9654882a82bb93278b506a0d", "sourceName": "src/contracts/strategies/StrategyFactory.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyFactoryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyFactory": {"0.8.12": {"path": "StrategyFactory.sol/StrategyFactory.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/strategies/StrategyFactoryStorage.sol": {"lastModificationDate": 1730704250574, "contentHash": "46ec5b2ef9cf0972e79c2535385f1490", "sourceName": "src/contracts/strategies/StrategyFactoryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyFactoryStorage": {"0.8.12": {"path": "StrategyFactoryStorage.sol/StrategyFactoryStorage.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/token/BackingEigen.sol": {"lastModificationDate": 1730736797185, "contentHash": "c8b84fad388f868e6206398dbe815502", "sourceName": "src/contracts/token/BackingEigen.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BackingEigen": {"0.8.12": {"path": "BackingEigen.sol/BackingEigen.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/token/Eigen.sol": {"lastModificationDate": 1730736797194, "contentHash": "e22f170e1e22a39e88a875d75b0cabaa", "sourceName": "src/contracts/token/Eigen.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Eigen": {"0.8.12": {"path": "Eigen.sol/Eigen.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/contracts/utils/UpgradeableSignatureCheckingUtils.sol": {"lastModificationDate": 1730736796912, "contentHash": "d3aeaaba9c007b6011c20b1e4e49d198", "sourceName": "src/contracts/utils/UpgradeableSignatureCheckingUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"UpgradeableSignatureCheckingUtils": {"0.8.12": {"path": "UpgradeableSignatureCheckingUtils.sol/UpgradeableSignatureCheckingUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/Delegation.t.sol": {"lastModificationDate": 1730704250575, "contentHash": "581cd57cf8c0d1de9d00b57430fccbc5", "sourceName": "src/test/Delegation.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/EigenLayerDeployer.t.sol", "src/test/EigenLayerTestHelper.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationTests": {"0.8.12": {"path": "Delegation.t.sol/DelegationTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/DepositWithdraw.t.sol": {"lastModificationDate": 1730704250575, "contentHash": "f03780acdf80ac876bda4080e597c0f0", "sourceName": "src/test/DepositWithdraw.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/EigenLayerDeployer.t.sol", "src/test/EigenLayerTestHelper.t.sol", "src/test/mocks/ERC20_OneWeiFeeOnTransfer.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DepositWithdrawTests": {"0.8.12": {"path": "DepositWithdraw.t.sol/DepositWithdrawTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/EigenLayerDeployer.t.sol": {"lastModificationDate": 1730704250575, "contentHash": "72464797a05a577ecfd38ac4877d6d61", "sourceName": "src/test/EigenLayerDeployer.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenLayerDeployer": {"0.8.12": {"path": "EigenLayerDeployer.t.sol/EigenLayerDeployer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/EigenLayerTestHelper.t.sol": {"lastModificationDate": 1730704250575, "contentHash": "2e3743ae9a77233654ab1edf39f20129", "sourceName": "src/test/EigenLayerTestHelper.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/EigenLayerDeployer.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenLayerTestHelper": {"0.8.12": {"path": "EigenLayerTestHelper.t.sol/EigenLayerTestHelper.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/Pausable.t.sol": {"lastModificationDate": 1730704250575, "contentHash": "83f0ddebb083427c1b3e168d1cf4747c", "sourceName": "src/test/Pausable.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/EigenLayerDeployer.t.sol", "src/test/EigenLayerTestHelper.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PausableTests": {"0.8.12": {"path": "Pausable.t.sol/PausableTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/Strategy.t.sol": {"lastModificationDate": 1730704250575, "contentHash": "03ba440f2b5e9fe88a2e1983aef20796", "sourceName": "src/test/Strategy.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/EigenLayerDeployer.t.sol", "src/test/EigenLayerTestHelper.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyTests": {"0.8.12": {"path": "Strategy.t.sol/StrategyTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/Withdrawals.t.sol": {"lastModificationDate": 1730704250576, "contentHash": "f1e885e5761abfb9d0645bd0688ab7cc", "sourceName": "src/test/Withdrawals.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/EigenLayerDeployer.t.sol", "src/test/EigenLayerTestHelper.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol", "src/test/mocks/LiquidStakingToken.sol", "src/test/utils/Operators.sol"], "versionRequirement": "^0.8.12", "artifacts": {"WithdrawalTests": {"0.8.12": {"path": "Withdrawals.t.sol/WithdrawalTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/events/IAVSDirectoryEvents.sol": {"lastModificationDate": 1730704250576, "contentHash": "f919ee548c0b92b0130025e8dfe7f686", "sourceName": "src/test/events/IAVSDirectoryEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/ISignatureUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IAVSDirectoryEvents": {"0.8.12": {"path": "IAVSDirectoryEvents.sol/IAVSDirectoryEvents.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/events/IDelegationManagerEvents.sol": {"lastModificationDate": 1730704250576, "contentHash": "e4d62ebb2b11f5684f2236008d0a4ae9", "sourceName": "src/test/events/IDelegationManagerEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IDelegationManagerEvents": {"0.8.12": {"path": "IDelegationManagerEvents.sol/IDelegationManagerEvents.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/events/IEigenPodEvents.sol": {"lastModificationDate": 1730704250576, "contentHash": "2b8305e34075c5a89541a219270f30e8", "sourceName": "src/test/events/IEigenPodEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IEigenPodEvents": {"0.8.12": {"path": "IEigenPodEvents.sol/IEigenPodEvents.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/events/IEigenPodManagerEvents.sol": {"lastModificationDate": 1730704250577, "contentHash": "d1982211b5bbf63720631243ac955bf5", "sourceName": "src/test/events/IEigenPodManagerEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IEigenPodManagerEvents": {"0.8.12": {"path": "IEigenPodManagerEvents.sol/IEigenPodManagerEvents.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/events/IRewardsCoordinatorEvents.sol": {"lastModificationDate": 1730704250577, "contentHash": "ed384a8c6b7270b8791719c4c8dbb271", "sourceName": "src/test/events/IRewardsCoordinatorEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/IStrategy.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IRewardsCoordinatorEvents": {"0.8.12": {"path": "IRewardsCoordinatorEvents.sol/IRewardsCoordinatorEvents.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/events/IStrategyManagerEvents.sol": {"lastModificationDate": 1730704250577, "contentHash": "365bc3914df1f260fbaf0ee637650ed2", "sourceName": "src/test/events/IStrategyManagerEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IStrategyManagerEvents": {"0.8.12": {"path": "IStrategyManagerEvents.sol/IStrategyManagerEvents.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/harnesses/EigenHarness.sol": {"lastModificationDate": 1730704250577, "contentHash": "e1c4d6cf76e0084472ff2411a643799c", "sourceName": "src/test/harnesses/EigenHarness.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "src/contracts/token/Eigen.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenHarness": {"0.8.12": {"path": "EigenHarness.sol/EigenHarness.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/harnesses/EigenPodHarness.sol": {"lastModificationDate": 1730704250577, "contentHash": "f87abbf00585379b9fcbcab100c8a152", "sourceName": "src/test/harnesses/EigenPodHarness.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodHarness": {"0.8.12": {"path": "EigenPodHarness.sol/EigenPodHarness.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/harnesses/EigenPodManagerWrapper.sol": {"lastModificationDate": 1730704250577, "contentHash": "b5f287d145dc70639dce9551b7224839", "sourceName": "src/test/harnesses/EigenPodManagerWrapper.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodManagerWrapper": {"0.8.12": {"path": "EigenPodManagerWrapper.sol/EigenPodManagerWrapper.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/harnesses/PausableHarness.sol": {"lastModificationDate": 1730704250577, "contentHash": "3d6e387e0247859e1d33360365850b7d", "sourceName": "src/test/harnesses/PausableHarness.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PausableHarness": {"0.8.12": {"path": "PausableHarness.sol/PausableHarness.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/IntegrationBase.t.sol": {"lastModificationDate": 1730704250578, "contentHash": "1ba661fde0e6789b1fa7c50a78934f56", "sourceName": "src/test/integration/IntegrationBase.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationBase": {"0.8.12": {"path": "IntegrationBase.t.sol/IntegrationBase.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/IntegrationChecks.t.sol": {"lastModificationDate": 1730704250578, "contentHash": "b66889d01467f6e89cbed3713b189a6b", "sourceName": "src/test/integration/IntegrationChecks.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationCheckUtils": {"0.8.12": {"path": "IntegrationChecks.t.sol/IntegrationCheckUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/IntegrationDeployer.t.sol": {"lastModificationDate": 1730704250578, "contentHash": "54a58820feac09b98b758ac112e18faf", "sourceName": "src/test/integration/IntegrationDeployer.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationDeployer": {"0.8.12": {"path": "IntegrationDeployer.t.sol/IntegrationDeployer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/TimeMachine.t.sol": {"lastModificationDate": 1730704250578, "contentHash": "8ba1f96b2e4a150bfd84c3ebb5acee0c", "sourceName": "src/test/integration/TimeMachine.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": "^0.8.12", "artifacts": {"TimeMachine": {"0.8.12": {"path": "TimeMachine.t.sol/TimeMachine.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol": {"lastModificationDate": 1730704250578, "contentHash": "e40de6b2330141c1f328cad13fde3f3c", "sourceName": "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BeaconChainProofs_DeprecatedM1": {"0.8.12": {"path": "BeaconChainProofs.sol/BeaconChainProofs_DeprecatedM1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol": {"lastModificationDate": 1730704250579, "contentHash": "5cd63d7260e8fb11ca4935290ea85d10", "sourceName": "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IBeaconChainOracle_DeprecatedM1": {"0.8.12": {"path": "IBeaconChainOracle.sol/IBeaconChainOracle_DeprecatedM1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/deprecatedInterfaces/mainnet/IDelayedWithdrawalRouter.sol": {"lastModificationDate": 1730704250579, "contentHash": "5f3a294011b87a2ff2b614ff4c396a73", "sourceName": "src/test/integration/deprecatedInterfaces/mainnet/IDelayedWithdrawalRouter.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IDelayedWithdrawalRouter_DeprecatedM1": {"0.8.12": {"path": "IDelayedWithdrawalRouter.sol/IDelayedWithdrawalRouter_DeprecatedM1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol": {"lastModificationDate": 1730704250579, "contentHash": "7f903fc46697812e80dfe6961c42af5f", "sourceName": "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IEigenPod_DeprecatedM1": {"0.8.12": {"path": "IEigenPod.sol/IEigenPod_DeprecatedM1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol": {"lastModificationDate": 1730704250579, "contentHash": "1f01ca21742714c55ec89f35b7b1818a", "sourceName": "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IEigenPodManager_DeprecatedM1": {"0.8.12": {"path": "IEigenPodManager.sol/IEigenPodManager_DeprecatedM1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol": {"lastModificationDate": 1730704250579, "contentHash": "e1c618875646db4be50ec87ef9e95ca4", "sourceName": "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IStrategyManager_DeprecatedM1": {"0.8.12": {"path": "IStrategyManager.sol/IStrategyManager_DeprecatedM1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/mocks/BeaconChainMock.t.sol": {"lastModificationDate": 1730704250579, "contentHash": "ef330982619b86b9b1ca93b96ecdc103", "sourceName": "src/test/integration/mocks/BeaconChainMock.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/utils/PrintUtils.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BeaconChainMock": {"0.8.12": {"path": "BeaconChainMock.t.sol/BeaconChainMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol": {"lastModificationDate": 1730704250580, "contentHash": "4977034c47601f157fb4b2c3bf925bd8", "sourceName": "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"EIP_4788_Oracle_Mock": {"0.8.12": {"path": "EIP_4788_Oracle_Mock.t.sol/EIP_4788_Oracle_Mock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Delegate_Deposit_Queue_Complete.t.sol": {"lastModificationDate": 1730704250580, "contentHash": "ecb798da63d9ff72a96509fc15551bea", "sourceName": "src/test/integration/tests/Delegate_Deposit_Queue_Complete.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Delegate_Deposit_Queue_Complete": {"0.8.12": {"path": "Delegate_Deposit_Queue_Complete.t.sol/Integration_Delegate_Deposit_Queue_Complete.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Deposit_Delegate_Queue_Complete.t.sol": {"lastModificationDate": 1730704250580, "contentHash": "9aaa5e201e31e4b39c477495339cd0e8", "sourceName": "src/test/integration/tests/Deposit_Delegate_Queue_Complete.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Deposit_Delegate_Queue_Complete": {"0.8.12": {"path": "Deposit_Delegate_Queue_Complete.t.sol/Integration_Deposit_Delegate_Queue_Complete.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Deposit_Delegate_Redelegate_Complete.t.sol": {"lastModificationDate": 1730704250580, "contentHash": "4d2296dff95c296ebd1a2ef8ce46f748", "sourceName": "src/test/integration/tests/Deposit_Delegate_Redelegate_Complete.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Deposit_Delegate_Redelegate_Complete": {"0.8.12": {"path": "Deposit_Delegate_Redelegate_Complete.t.sol/Integration_Deposit_Delegate_Redelegate_Complete.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Deposit_Delegate_Undelegate_Complete.t.sol": {"lastModificationDate": 1730704250580, "contentHash": "3df583ee8b0a42334c3235d382de8774", "sourceName": "src/test/integration/tests/Deposit_Delegate_Undelegate_Complete.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Deposit_Delegate_Undelegate_Complete": {"0.8.12": {"path": "Deposit_Delegate_Undelegate_Complete.t.sol/Integration_Deposit_Delegate_Undelegate_Complete.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Deposit_Delegate_UpdateBalance.t.sol": {"lastModificationDate": 1730704250581, "contentHash": "727f8357a5b8f7bcddb75e88f88d9079", "sourceName": "src/test/integration/tests/Deposit_Delegate_UpdateBalance.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Deposit_Delegate_UpdateBalance": {"0.8.12": {"path": "Deposit_Delegate_UpdateBalance.t.sol/Integration_Deposit_Delegate_UpdateBalance.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Deposit_Queue_Complete.t.sol": {"lastModificationDate": 1730704250581, "contentHash": "16ec10476cf8dfe2eaae11efcc717a2f", "sourceName": "src/test/integration/tests/Deposit_Queue_Complete.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Deposit_QueueWithdrawal_Complete": {"0.8.12": {"path": "Deposit_Queue_Complete.t.sol/Integration_Deposit_QueueWithdrawal_Complete.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Deposit_Register_QueueWithdrawal_Complete.t.sol": {"lastModificationDate": 1730704250581, "contentHash": "a4f1d65a0c254c114dfae8cf60b6dd1e", "sourceName": "src/test/integration/tests/Deposit_Register_QueueWithdrawal_Complete.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Deposit_Register_QueueWithdrawal_Complete": {"0.8.12": {"path": "Deposit_Register_QueueWithdrawal_Complete.t.sol/Integration_Deposit_Register_QueueWithdrawal_Complete.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/Upgrade_Setup.t.sol": {"lastModificationDate": 1730704250581, "contentHash": "8cedc8898632481e5046bbbcbdcea6f9", "sourceName": "src/test/integration/tests/Upgrade_Setup.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationMainnetFork_UpgradeSetup": {"0.8.12": {"path": "Upgrade_Setup.t.sol/IntegrationMainnetFork_UpgradeSetup.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/tests/eigenpod/VerifyWC_StartCP_CompleteCP.t.sol": {"lastModificationDate": 1730704250581, "contentHash": "b047392fde9c6ea4abb9eca048f4a2fa", "sourceName": "src/test/integration/tests/eigenpod/VerifyWC_StartCP_CompleteCP.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "script/utils/ExistingDeploymentParser.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/Slasher.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IBackingEigen.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigen.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/contracts/strategies/EigenStrategy.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/integration/IntegrationBase.t.sol", "src/test/integration/IntegrationChecks.t.sol", "src/test/integration/IntegrationDeployer.t.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/users/User_M1.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EmptyContract.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_VerifyWC_StartCP_CompleteCP": {"0.8.12": {"path": "VerifyWC_StartCP_CompleteCP.t.sol/Integration_VerifyWC_StartCP_CompleteCP.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/users/User.t.sol": {"lastModificationDate": 1730704250581, "contentHash": "7ccc855d26f7ac7d4c4738157edf211e", "sourceName": "src/test/integration/users/User.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/utils/PrintUtils.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IUserDeployer": {"0.8.12": {"path": "User.t.sol/IUserDeployer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "User": {"0.8.12": {"path": "User.t.sol/User.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "User_AltMethods": {"0.8.12": {"path": "User.t.sol/User_AltMethods.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/users/User_M1.t.sol": {"lastModificationDate": 1730704250582, "contentHash": "6f5cfff997f682438193e79c14abc097", "sourceName": "src/test/integration/users/User_M1.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/deprecatedInterfaces/mainnet/BeaconChainProofs.sol", "src/test/integration/deprecatedInterfaces/mainnet/IBeaconChainOracle.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPod.sol", "src/test/integration/deprecatedInterfaces/mainnet/IEigenPodManager.sol", "src/test/integration/deprecatedInterfaces/mainnet/IStrategyManager.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/users/User.t.sol", "src/test/integration/utils/PrintUtils.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IUserMainnetForkDeployer": {"0.8.12": {"path": "User_M1.t.sol/IUserMainnetForkDeployer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "User_M1": {"0.8.12": {"path": "User_M1.t.sol/User_M1.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "User_M1_AltMethods": {"0.8.12": {"path": "User_M1.t.sol/User_M1_AltMethods.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/integration/utils/PrintUtils.t.sol": {"lastModificationDate": 1730704250582, "contentHash": "732196d28f33b8a6e1cd17a21df017f6", "sourceName": "src/test/integration/utils/PrintUtils.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PrintUtils": {"0.8.12": {"path": "PrintUtils.t.sol/PrintUtils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/DelegationManagerMock.sol": {"lastModificationDate": 1730704250582, "contentHash": "a56b44282056a290837bbd49cf6f3fd8", "sourceName": "src/test/mocks/DelegationManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationManagerMock": {"0.8.12": {"path": "DelegationManagerMock.sol/DelegationManagerMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/Dummy.sol": {"lastModificationDate": 1730704250582, "contentHash": "ccd00bae8ad9766e82a263f159118c9f", "sourceName": "src/test/mocks/Dummy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"EmptyContract": {"0.8.12": {"path": "Dummy.sol/EmptyContract.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/ERC20Mock.sol": {"lastModificationDate": 1730704250582, "contentHash": "a76ac23c51c940b0b91ab81d8b141398", "sourceName": "src/test/mocks/ERC20Mock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ERC20Mock": {"0.8.12": {"path": "ERC20Mock.sol/ERC20Mock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/ERC20_OneWeiFeeOnTransfer.sol": {"lastModificationDate": 1730704250582, "contentHash": "fb6288b44a84924acb48aba0f9e9a6dc", "sourceName": "src/test/mocks/ERC20_OneWeiFeeOnTransfer.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"ERC20_OneWeiFeeOnTransfer": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/ERC20_OneWeiFeeOnTransfer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "OpenZeppelin_Context": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/OpenZeppelin_Context.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "OpenZeppelin_ERC20": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/OpenZeppelin_ERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "OpenZeppelin_ERC20Burnable": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/OpenZeppelin_ERC20Burnable.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "OpenZeppelin_ERC20PresetFixedSupply": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/OpenZeppelin_ERC20PresetFixedSupply.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "OpenZeppelin_IERC20": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/OpenZeppelin_IERC20.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "OpenZeppelin_IERC20Metadata": {"0.8.12": {"path": "ERC20_OneWeiFeeOnTransfer.sol/OpenZeppelin_IERC20Metadata.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/ERC20_SetTransferReverting_Mock.sol": {"lastModificationDate": 1730704250582, "contentHash": "daa420ba6ac5d50cbe5919ef372434ae", "sourceName": "src/test/mocks/ERC20_SetTransferReverting_Mock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ERC20_SetTransferReverting_Mock": {"0.8.12": {"path": "ERC20_SetTransferReverting_Mock.sol/ERC20_SetTransferReverting_Mock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/ETHDepositMock.sol": {"lastModificationDate": 1730704250583, "contentHash": "a91b7a2762ba92f64c1019b3244e4f05", "sourceName": "src/test/mocks/ETHDepositMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/interfaces/IETHPOSDeposit.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ETHPOSDepositMock": {"0.8.12": {"path": "ETHDepositMock.sol/ETHPOSDepositMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/EigenPodManagerMock.sol": {"lastModificationDate": 1730704250583, "contentHash": "22f633654ed79de8d778507623e62d46", "sourceName": "src/test/mocks/EigenPodManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.9", "artifacts": {"EigenPodManagerMock": {"0.8.12": {"path": "EigenPodManagerMock.sol/EigenPodManagerMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/EigenPodMock.sol": {"lastModificationDate": 1730704250583, "contentHash": "e148780ad34d54e119ef8aa1d75c486b", "sourceName": "src/test/mocks/EigenPodMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.9", "artifacts": {"EigenPodMock": {"0.8.12": {"path": "EigenPodMock.sol/EigenPodMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/EmptyContract.sol": {"lastModificationDate": 1730704250583, "contentHash": "ccd00bae8ad9766e82a263f159118c9f", "sourceName": "src/test/mocks/EmptyContract.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"EmptyContract": {"0.8.12": {"path": "EmptyContract.sol/EmptyContract.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/LiquidStakingToken.sol": {"lastModificationDate": 1730704250583, "contentHash": "e75d54e6eace2ca28521db0a9a2ff16c", "sourceName": "src/test/mocks/LiquidStakingToken.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"WETH": {"0.8.12": {"path": "LiquidStakingToken.sol/WETH.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/MockDecimals.sol": {"lastModificationDate": 1730704250583, "contentHash": "680a004760fc310c62c4183c9b8e024b", "sourceName": "src/test/mocks/MockDecimals.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"MockDecimals": {"0.8.12": {"path": "MockDecimals.sol/MockDecimals.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/OwnableMock.sol": {"lastModificationDate": 1730704250583, "contentHash": "ab92343c6d963c6924f718ac8875bf16", "sourceName": "src/test/mocks/OwnableMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.12", "artifacts": {"OwnableMock": {"0.8.12": {"path": "OwnableMock.sol/OwnableMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/Reenterer.sol": {"lastModificationDate": 1730704250583, "contentHash": "07d479b4aabfdff3ce8c758786794c1b", "sourceName": "src/test/mocks/Reenterer.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": "^0.8.9", "artifacts": {"Reenterer": {"0.8.12": {"path": "Reenterer.sol/Reenterer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/Reverter.sol": {"lastModificationDate": 1730704250583, "contentHash": "584e3be6977ea55f6865ac1dcc18ff46", "sourceName": "src/test/mocks/Reverter.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.9", "artifacts": {"Reverter": {"0.8.12": {"path": "Reverter.sol/Reverter.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "ReverterWithDecimals": {"0.8.12": {"path": "Reverter.sol/ReverterWithDecimals.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/SlasherMock.sol": {"lastModificationDate": 1730704250584, "contentHash": "11c1cfac30472adc1c993d93e9ef893a", "sourceName": "src/test/mocks/SlasherMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"SlasherMock": {"0.8.12": {"path": "SlasherMock.sol/SlasherMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/mocks/StrategyManagerMock.sol": {"lastModificationDate": 1730736797199, "contentHash": "79f4c148744a85045ef882822b76b9c9", "sourceName": "src/test/mocks/StrategyManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManagerMock": {"0.8.12": {"path": "StrategyManagerMock.sol/StrategyManagerMock.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/token/EigenTransferRestrictions.t.sol": {"lastModificationDate": 1730704250590, "contentHash": "6a334b3adcd920e40166bf63e10eafff", "sourceName": "src/test/token/EigenTransferRestrictions.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/access/Ownable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Address.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/Context.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/utils/StorageSlot.sol", "src/contracts/token/Eigen.sol", "src/test/harnesses/EigenHarness.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenTransferRestrictionsTest": {"0.8.12": {"path": "EigenTransferRestrictions.t.sol/EigenTransferRestrictionsTest.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/token/EigenWrapping.t.sol": {"lastModificationDate": 1730704250590, "contentHash": "48e08a354a8a9d733309f06085a91794", "sourceName": "src/test/token/EigenWrapping.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "src/contracts/token/BackingEigen.sol", "src/contracts/token/Eigen.sol", "src/test/harnesses/EigenHarness.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenWrappingTests": {"0.8.12": {"path": "EigenWrapping.t.sol/EigenWrappingTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/token/bEIGEN.t.sol": {"lastModificationDate": 1730704250590, "contentHash": "f2cc0168114f12562f3d26430659f2aa", "sourceName": "src/test/token/bEIGEN.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/governance/utils/IVotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5267Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC5805Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/interfaces/IERC6372Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/ERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/IERC20Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/CountersUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/cryptography/EIP712Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SafeCastUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable-v4.9.0/contracts/utils/math/SignedMathUpgradeable.sol", "lib/openzeppelin-contracts-v4.9.0/contracts/token/ERC20/IERC20.sol", "src/contracts/token/BackingEigen.sol", "src/contracts/token/Eigen.sol", "src/test/harnesses/EigenHarness.sol"], "versionRequirement": "^0.8.12", "artifacts": {"bEIGENTest": {"0.8.12": {"path": "bEIGEN.t.sol/bEIGENTest.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/AVSDirectoryUnit.t.sol": {"lastModificationDate": 1730704250591, "contentHash": "d1733e00cea58226e5cc5bbea57265d2", "sourceName": "src/test/unit/AVSDirectoryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/AVSDirectory.sol", "src/contracts/core/AVSDirectoryStorage.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IAVSDirectory.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/test/events/IAVSDirectoryEvents.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol"], "versionRequirement": "^0.8.12", "artifacts": {"AVSDirectoryUnitTests": {"0.8.12": {"path": "AVSDirectoryUnit.t.sol/AVSDirectoryUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "AVSDirectoryUnitTests_operatorAVSRegisterationStatus": {"0.8.12": {"path": "AVSDirectoryUnit.t.sol/AVSDirectoryUnitTests_operatorAVSRegisterationStatus.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/DelegationUnit.t.sol": {"lastModificationDate": 1730704250592, "contentHash": "43dfb92d757aaca92256bac8650e6002", "sourceName": "src/test/unit/DelegationUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/DelegationManager.sol", "src/contracts/core/DelegationManagerStorage.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/events/IDelegationManagerEvents.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationManagerUnitTests": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_Initialization_Setters": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_Initialization_Setters.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_RegisterModifyOperator": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_RegisterModifyOperator.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_ShareAdjustment": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_ShareAdjustment.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_Undelegate": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_Undelegate.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_completeQueuedWithdrawal": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_completeQueuedWithdrawal.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_delegateTo": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_delegateTo.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_delegateToBySignature": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_delegateToBySignature.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "DelegationManagerUnitTests_queueWithdrawals": {"0.8.12": {"path": "DelegationUnit.t.sol/DelegationManagerUnitTests_queueWithdrawals.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/EigenPodManagerUnit.t.sol": {"lastModificationDate": 1730704250592, "contentHash": "f74c381afd5b176780ccc446ba49e96f", "sourceName": "src/test/unit/EigenPodManagerUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/test/events/IEigenPodManagerEvents.sol", "src/test/harnesses/EigenPodManagerWrapper.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/EigenPodMock.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodManagerUnitTests": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodManagerUnitTests_BeaconChainETHBalanceUpdateTests": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests_BeaconChainETHBalanceUpdateTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodManagerUnitTests_CreationTests": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests_CreationTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodManagerUnitTests_Initialization_Setters": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests_Initialization_Setters.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodManagerUnitTests_ShareAdjustmentCalculationTests": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests_ShareAdjustmentCalculationTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodManagerUnitTests_ShareUpdateTests": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests_ShareUpdateTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodManagerUnitTests_StakeTests": {"0.8.12": {"path": "EigenPodManagerUnit.t.sol/EigenPodManagerUnitTests_StakeTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/EigenPodUnit.t.sol": {"lastModificationDate": 1730704250592, "contentHash": "7cbac1ace3dfd2e9dce964c39a512789", "sourceName": "src/test/unit/EigenPodUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/test/events/IEigenPodEvents.sol", "src/test/harnesses/EigenPodHarness.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/utils/PrintUtils.t.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/ERC20Mock.sol", "src/test/mocks/ETHDepositMock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol", "src/test/utils/EigenPodUser.t.sol", "src/test/utils/ProofParsing.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodHarnessSetup": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodHarnessSetup.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_EPMFunctions": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_EPMFunctions.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_Initialization": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_Initialization.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_proofParsingTests": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_proofParsingTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_recoverTokens": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_recoverTokens.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_startCheckpoint": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_startCheckpoint.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_verifyCheckpointProofs": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_verifyCheckpointProofs.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_verifyStaleBalance": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_verifyStaleBalance.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "EigenPodUnitTests_verifyWithdrawalCredentials": {"0.8.12": {"path": "EigenPodUnit.t.sol/EigenPodUnitTests_verifyWithdrawalCredentials.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/PausableUnit.t.sol": {"lastModificationDate": 1730704250592, "contentHash": "db806d2ccc6d8b73948001ba3095563c", "sourceName": "src/test/unit/PausableUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/test/harnesses/PausableHarness.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PausableUnitTests": {"0.8.12": {"path": "PausableUnit.t.sol/PausableUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/PauserRegistryUnit.t.sol": {"lastModificationDate": 1730704250592, "contentHash": "dba068d74f86fc792d20532125db7d61", "sourceName": "src/test/unit/PauserRegistryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/permissions/PauserRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PauserRegistryUnitTests": {"0.8.12": {"path": "PauserRegistryUnit.t.sol/PauserRegistryUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/RewardsCoordinatorUnit.t.sol": {"lastModificationDate": 1730704250592, "contentHash": "c3bd0ce9b962e70be82f87d41b61e42b", "sourceName": "src/test/unit/RewardsCoordinatorUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/RewardsCoordinator.sol", "src/contracts/core/RewardsCoordinatorStorage.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/IRewardsCoordinator.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/events/IRewardsCoordinatorEvents.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/ERC20Mock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/Reenterer.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RewardsCoordinatorUnitTests": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "RewardsCoordinatorUnitTests_createAVSRewardsSubmission": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests_createAVSRewardsSubmission.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "RewardsCoordinatorUnitTests_createRewardsForAllEarners": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests_createRewardsForAllEarners.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "RewardsCoordinatorUnitTests_createRewardsForAllSubmission": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests_createRewardsForAllSubmission.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "RewardsCoordinatorUnitTests_initializeAndSetters": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests_initializeAndSetters.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "RewardsCoordinatorUnitTests_processClaim": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests_processClaim.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "RewardsCoordinatorUnitTests_submitRoot": {"0.8.12": {"path": "RewardsCoordinatorUnit.t.sol/RewardsCoordinatorUnitTests_submitRoot.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/StrategyBaseTVLLimitsUnit.sol": {"lastModificationDate": 1730704250592, "contentHash": "3ae3f13384e86f9dee2071f1dad3e753", "sourceName": "src/test/unit/StrategyBaseTVLLimitsUnit.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyBaseTVLLimits.sol", "src/test/mocks/ERC20_SetTransferReverting_Mock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/unit/StrategyBaseUnit.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyBaseTVLLimitsUnitTests": {"0.8.12": {"path": "StrategyBaseTVLLimitsUnit.sol/StrategyBaseTVLLimitsUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/StrategyBaseUnit.t.sol": {"lastModificationDate": 1730704250593, "contentHash": "8b918ded47ed2a646d10a9e193445eba", "sourceName": "src/test/unit/StrategyBaseUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/mocks/ERC20_SetTransferReverting_Mock.sol", "src/test/mocks/StrategyManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyBaseUnitTests": {"0.8.12": {"path": "StrategyBaseUnit.t.sol/StrategyBaseUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/StrategyFactoryUnit.t.sol": {"lastModificationDate": 1730704250593, "contentHash": "efef4ebe94f523af569ba51ea109a44d", "sourceName": "src/test/unit/StrategyFactoryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyFactory.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/strategies/StrategyBase.sol", "src/contracts/strategies/StrategyFactory.sol", "src/contracts/strategies/StrategyFactoryStorage.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyFactoryUnitTests": {"0.8.12": {"path": "StrategyFactoryUnit.t.sol/StrategyFactoryUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/unit/StrategyManagerUnit.t.sol": {"lastModificationDate": 1730704250593, "contentHash": "0bf5eeabf9ade73a874de94fb3cdded4", "sourceName": "src/test/unit/StrategyManagerUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/mocks/ERC1271WalletMock.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManager.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/EIP1271SignatureUtils.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/contracts/strategies/StrategyBase.sol", "src/test/events/IStrategyManagerEvents.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/ERC20Mock.sol", "src/test/mocks/ERC20_SetTransferReverting_Mock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/MockDecimals.sol", "src/test/mocks/Reenterer.sol", "src/test/mocks/Reverter.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol", "src/test/utils/EigenLayerUnitTestSetup.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManagerUnitTests": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_addShares": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_addShares.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_addStrategiesToDepositWhitelist": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_addStrategiesToDepositWhitelist.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_depositIntoStrategy": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_depositIntoStrategy.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_depositIntoStrategyWithSignature": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_depositIntoStrategyWithSignature.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_initialize": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_initialize.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_removeShares": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_removeShares.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_removeStrategiesFromDepositWhitelist": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_removeStrategiesFromDepositWhitelist.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_setStrategyWhitelister": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_setStrategyWhitelister.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "StrategyManagerUnitTests_withdrawSharesAsTokens": {"0.8.12": {"path": "StrategyManagerUnit.t.sol/StrategyManagerUnitTests_withdrawSharesAsTokens.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/BeaconChainProofsWrapper.sol": {"lastModificationDate": 1730704250593, "contentHash": "f8b96c3d6f47ceb48a9cd143ffaa0467", "sourceName": "src/test/utils/BeaconChainProofsWrapper.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BeaconChainProofsWrapper": {"0.8.12": {"path": "BeaconChainProofsWrapper.sol/BeaconChainProofsWrapper.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/EigenLayerUnitTestBase.sol": {"lastModificationDate": 1730704250593, "contentHash": "f799cfd635e515f0f863771667b48f7f", "sourceName": "src/test/utils/EigenLayerUnitTestBase.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/permissions/PauserRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenLayerUnitTestBase": {"0.8.12": {"path": "EigenLayerUnitTestBase.sol/EigenLayerUnitTestBase.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/EigenLayerUnitTestSetup.sol": {"lastModificationDate": 1730704250594, "contentHash": "21ee1ccfa4092bfaff945ce21eef556a", "sourceName": "src/test/utils/EigenLayerUnitTestSetup.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/core/StrategyManagerStorage.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/permissions/PauserRegistry.sol", "src/test/mocks/DelegationManagerMock.sol", "src/test/mocks/EigenPodManagerMock.sol", "src/test/mocks/SlasherMock.sol", "src/test/mocks/StrategyManagerMock.sol", "src/test/utils/EigenLayerUnitTestBase.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenLayerUnitTestSetup": {"0.8.12": {"path": "EigenLayerUnitTestSetup.sol/EigenLayerUnitTestSetup.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/EigenPodUser.t.sol": {"lastModificationDate": 1730704250594, "contentHash": "c66b329e61a2613a4cdf3559f11992fc", "sourceName": "src/test/utils/EigenPodUser.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/BytesLib.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/pods/EigenPod.sol", "src/contracts/pods/EigenPodManager.sol", "src/contracts/pods/EigenPodManagerStorage.sol", "src/contracts/pods/EigenPodPausingConstants.sol", "src/contracts/pods/EigenPodStorage.sol", "src/test/integration/TimeMachine.t.sol", "src/test/integration/mocks/BeaconChainMock.t.sol", "src/test/integration/mocks/EIP_4788_Oracle_Mock.t.sol", "src/test/integration/utils/PrintUtils.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodUser": {"0.8.12": {"path": "EigenPodUser.t.sol/EigenPodUser.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}, "IUserDeployer": {"0.8.12": {"path": "EigenPodUser.t.sol/IUserDeployer.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/Operators.sol": {"lastModificationDate": 1730704250594, "contentHash": "ca3ff12e10ef30ae858712114a828000", "sourceName": "src/test/utils/Operators.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Operators": {"0.8.12": {"path": "Operators.sol/Operators.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/Owners.sol": {"lastModificationDate": 1730704250594, "contentHash": "6719637fb23311ca26f494dbaa1e8c95", "sourceName": "src/test/utils/Owners.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Owners": {"0.8.12": {"path": "Owners.sol/Owners.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/ProofParsing.sol": {"lastModificationDate": 1730704250594, "contentHash": "5dee914d14b6b510022c95921a5e6ebc", "sourceName": "src/test/utils/ProofParsing.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/ds-test/src/test.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ProofParsing": {"0.8.12": {"path": "ProofParsing.sol/ProofParsing.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/SignatureCompaction.sol": {"lastModificationDate": 1730704250594, "contentHash": "27a84e2f29ceed4c3127cef9d635dcb5", "sourceName": "src/test/utils/SignatureCompaction.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol"], "versionRequirement": "^0.8.12", "artifacts": {"SignatureCompaction": {"0.8.12": {"path": "SignatureCompaction.sol/SignatureCompaction.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}, "src/test/utils/Utils.sol": {"lastModificationDate": 1730704250594, "contentHash": "eacc754da6321912bda0bd233ba3d214", "sourceName": "src/test/utils/Utils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/contracts/interfaces/IDelegationManager.sol", "src/contracts/interfaces/IETHPOSDeposit.sol", "src/contracts/interfaces/IEigenPod.sol", "src/contracts/interfaces/IEigenPodManager.sol", "src/contracts/interfaces/IPausable.sol", "src/contracts/interfaces/IPauserRegistry.sol", "src/contracts/interfaces/ISignatureUtils.sol", "src/contracts/interfaces/ISlasher.sol", "src/contracts/interfaces/IStrategy.sol", "src/contracts/interfaces/IStrategyManager.sol", "src/contracts/libraries/BeaconChainProofs.sol", "src/contracts/libraries/Endian.sol", "src/contracts/libraries/Merkle.sol", "src/contracts/permissions/Pausable.sol", "src/contracts/strategies/StrategyBase.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Utils": {"0.8.12": {"path": "Utils.sol/Utils.json", "build_id": "9b9646b2babdf357b8299628e577b688"}}}, "seenByCompiler": true}}, "builds": ["9b9646b2babdf357b8299628e577b688"]}