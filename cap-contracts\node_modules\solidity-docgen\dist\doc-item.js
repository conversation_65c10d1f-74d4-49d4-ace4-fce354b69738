"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isDocItem = exports.docItemTypes = void 0;
exports.docItemTypes = [
    'ContractDefinition',
    'EnumDefinition',
    'ErrorDefinition',
    'EventDefinition',
    'FunctionDefinition',
    'ModifierDefinition',
    'StructDefinition',
    'UserDefinedValueTypeDefinition',
    'VariableDeclaration',
];
// Make sure at compile time that docItemTypes contains exactly the node types of DocItem.
const _ = true;
function isDocItem(node) {
    return exports.docItemTypes.includes(node.nodeType);
}
exports.isDocItem = isDocItem;
//# sourceMappingURL=doc-item.js.map