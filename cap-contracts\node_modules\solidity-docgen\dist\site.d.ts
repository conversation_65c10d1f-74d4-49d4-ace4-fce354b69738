import { ContractDefinition, SourceUnit } from 'solidity-ast';
import { SolcOutput, SolcInput } from 'solidity-ast/solc';
import { ASTDereferencer, SrcDecoder } from 'solidity-ast/utils';
import { FullConfig } from './config';
import { DocItem } from './doc-item';
import { Properties } from './templates';
export interface Build {
    input: SolcInput;
    output: SolcOutput;
}
export interface BuildContext extends Build {
    deref: ASTDereferencer;
    decodeSrc: SrcDecoder;
}
export type SiteConfig = Pick<FullConfig, 'pages' | 'exclude' | 'sourcesDir' | 'pageExtension'>;
export type PageStructure = SiteConfig['pages'];
export type PageAssigner = ((item: DocItem, file: SourceUnit, config: SiteConfig) => string | undefined);
export declare const pageAssigner: Record<PageStructure & string, PageAssigner>;
export interface Site {
    items: DocItemWithContext[];
    pages: Page[];
}
export interface Page {
    id: string;
    items: DocItemWithContext[];
}
export declare const DOC_ITEM_CONTEXT: "__item_context";
export type DocItemWithContext = DocItem & {
    [DOC_ITEM_CONTEXT]: DocItemContext;
};
export interface DocItemContext {
    page?: string;
    item: DocItemWithContext;
    contract?: ContractDefinition;
    file: DocItemContextFile;
    build: BuildContext;
}
export interface DocItemContextFile extends SourceUnit {
    relativePath: string;
}
export declare function buildSite(builds: Build[], siteConfig: SiteConfig, properties?: Properties): Site;
//# sourceMappingURL=site.d.ts.map