"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.docItemTypes = exports.docgen = void 0;
var main_1 = require("./main");
Object.defineProperty(exports, "docgen", { enumerable: true, get: function () { return main_1.main; } });
var doc_item_1 = require("./doc-item");
Object.defineProperty(exports, "docItemTypes", { enumerable: true, get: function () { return doc_item_1.docItemTypes; } });
require("./hardhat/type-extensions");
if ('extendConfig' in global && 'task' in global) {
    // Assume Hardhat.
    require('./hardhat');
}
// We ask Node.js not to cache this file.
delete require.cache[__filename];
//# sourceMappingURL=index.js.map