{"name": "recursive-copy", "version": "2.0.14", "description": "Simple, flexible file copy utility", "main": "index.js", "types": "index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["index.js", "index.d.ts", "lib"], "scripts": {"test": "npm run test:lint && npm run test:mocha && if-node-version '>=10' npm run test:typings", "test:lint": "if-node-version '>=4' eslint index.js test", "test:mocha": "mocha --reporter spec", "test:typings": "tsd && echo 'TypeScript definitions are valid'", "prepublishOnly": "npm run test"}, "repository": {"type": "git", "url": "https://github.com/timkendrick/recursive-copy.git"}, "keywords": ["copy", "recursive", "file", "directory", "folder", "symlink", "fs", "rename", "filter", "transform", "glob", "regex", "regexp"], "author": "<PERSON> <tim<PERSON><PERSON>@gmail.com>", "license": "ISC", "bugs": {"url": "https://github.com/timkendrick/recursive-copy/issues"}, "homepage": "https://github.com/timkendrick/recursive-copy", "dependencies": {"errno": "^0.1.2", "graceful-fs": "^4.1.4", "junk": "^1.0.1", "maximatch": "^0.1.0", "mkdirp": "^0.5.1", "pify": "^2.3.0", "promise": "^7.0.1", "rimraf": "^2.7.1", "slash": "^1.0.0"}, "devDependencies": {"@types/node": "^14.6.0", "chai": "^3.5.0", "chai-as-promised": "^5.3.0", "eslint": "^2.9.0", "if-node-version": "^1.1.1", "mocha": "^2.4.5", "read-dir-files": "^0.1.1", "rewire": "^2.3.3", "through2": "^2.0.1", "tsd": "0.12.1"}}