"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = void 0;
const path_1 = __importDefault(require("path"));
const fs_1 = require("fs");
const render_1 = require("./render");
const site_1 = require("./site");
const config_1 = require("./config");
const templates_1 = require("./templates");
/**
 * Given a set of builds (i.e. solc outputs) and a user configuration, this
 * function builds the site and renders it, writing all pages to the output
 * directory.
 */
async function main(builds, userConfig) {
    const config = { ...config_1.defaults, ...userConfig };
    const templates = await (0, templates_1.loadTemplates)(config.theme, config.root, config.templates);
    const site = (0, site_1.buildSite)(builds, config, templates.properties ?? {});
    const renderedSite = (0, render_1.render)(site, templates, config.collapseNewlines);
    for (const { id, contents } of renderedSite) {
        const outputFile = path_1.default.resolve(config.root, config.outputDir, id);
        await fs_1.promises.mkdir(path_1.default.dirname(outputFile), { recursive: true });
        await fs_1.promises.writeFile(outputFile, contents);
    }
}
exports.main = main;
//# sourceMappingURL=main.js.map