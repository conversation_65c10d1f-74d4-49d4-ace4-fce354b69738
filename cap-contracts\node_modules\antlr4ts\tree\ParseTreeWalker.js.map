{"version": 3, "file": "ParseTreeWalker.js", "sourceRoot": "", "sources": ["../../../src/tree/ParseTreeWalker.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAKH,2CAAwC;AACxC,iDAA8C;AAC9C,yCAAsC;AAGtC,MAAa,eAAe;IAC3B;;;;;;;OAOG;IACI,IAAI,CAA8B,QAAW,EAAE,CAAY;QACjE,IAAI,SAAS,GAAgB,EAAE,CAAC;QAChC,IAAI,UAAU,GAAa,EAAE,CAAC;QAE9B,IAAI,WAAW,GAA0B,CAAC,CAAC;QAC3C,IAAI,YAAY,GAAW,CAAC,CAAC;QAE7B,OAAO,WAAW,EAAE;YACnB,kBAAkB;YAClB,IAAI,WAAW,YAAY,qBAAS,EAAE;gBACrC,IAAI,QAAQ,CAAC,cAAc,EAAE;oBAC5B,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;iBACrC;aACD;iBAAM,IAAI,WAAW,YAAY,2BAAY,EAAE;gBAC/C,IAAI,QAAQ,CAAC,aAAa,EAAE;oBAC3B,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;iBACpC;aACD;iBAAM;gBACN,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAuB,CAAC,CAAC;aAClD;YAED,sCAAsC;YACtC,IAAI,WAAW,CAAC,UAAU,GAAG,CAAC,EAAE;gBAC/B,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC5B,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC9B,YAAY,GAAG,CAAC,CAAC;gBACjB,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtC,SAAS;aACT;YAED,+BAA+B;YAC/B,GAAG;gBACF,mBAAmB;gBACnB,IAAI,WAAW,YAAY,mBAAQ,EAAE;oBACpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;iBACrC;gBAED,4BAA4B;gBAC5B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3B,WAAW,GAAG,SAAS,CAAC;oBACxB,YAAY,GAAG,CAAC,CAAC;oBACjB,MAAM;iBACN;gBAED,mCAAmC;gBACnC,IAAI,IAAI,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3C,YAAY,EAAE,CAAC;gBACf,WAAW,GAAG,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACvF,IAAI,WAAW,EAAE;oBAChB,MAAM;iBACN;gBAED,8BAA8B;gBAC9B,WAAW,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;gBAC9B,YAAY,GAAG,UAAU,CAAC,GAAG,EAAG,CAAC;aACjC,QAAQ,WAAW,EAAE;SACtB;IACF,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,QAA2B,EAAE,CAAW;QAC3D,IAAI,GAAG,GAAG,CAAC,CAAC,WAAgC,CAAC;QAC7C,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC5B,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;SAC7B;QAED,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACO,QAAQ,CAAC,QAA2B,EAAE,CAAW;QAC1D,IAAI,GAAG,GAAG,CAAC,CAAC,WAAgC,CAAC;QAC7C,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvB,IAAI,QAAQ,CAAC,aAAa,EAAE;YAC3B,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;SAC5B;IACF,CAAC;CACD;AAhGD,0CAgGC;AAED,WAAiB,eAAe;IAClB,uBAAO,GAAoB,IAAI,eAAe,EAAE,CAAC;AAC/D,CAAC,EAFgB,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAE/B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.8252451-07:00\r\nimport { ParseTree } from \"./ParseTree\";\r\nimport { ParseTreeListener } from \"./ParseTreeListener\";\r\nimport { ErrorNode } from \"./ErrorNode\";\r\nimport { TerminalNode } from \"./TerminalNode\";\r\nimport { RuleNode } from \"./RuleNode\";\r\nimport { ParserRuleContext } from \"../ParserRuleContext\";\r\n\r\nexport class ParseTreeWalker {\r\n\t/**\r\n\t * Performs a walk on the given parse tree starting at the root and going down recursively\r\n\t * with depth-first search. On each node, {@link ParseTreeWalker#enterRule} is called before\r\n\t * recursively walking down into child nodes, then\r\n\t * {@link ParseTreeWalker#exitRule} is called after the recursive call to wind up.\r\n\t * @param listener The listener used by the walker to process grammar rules\r\n\t * @param t The parse tree to be walked on\r\n\t */\r\n\tpublic walk<T extends ParseTreeListener>(listener: T, t: ParseTree): void {\r\n\t\tlet nodeStack: ParseTree[] = [];\r\n\t\tlet indexStack: number[] = [];\r\n\r\n\t\tlet currentNode: ParseTree | undefined = t;\r\n\t\tlet currentIndex: number = 0;\r\n\r\n\t\twhile (currentNode) {\r\n\t\t\t// pre-order visit\r\n\t\t\tif (currentNode instanceof ErrorNode) {\r\n\t\t\t\tif (listener.visitErrorNode) {\r\n\t\t\t\t\tlistener.visitErrorNode(currentNode);\r\n\t\t\t\t}\r\n\t\t\t} else if (currentNode instanceof TerminalNode) {\r\n\t\t\t\tif (listener.visitTerminal) {\r\n\t\t\t\t\tlistener.visitTerminal(currentNode);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.enterRule(listener, currentNode as RuleNode);\r\n\t\t\t}\r\n\r\n\t\t\t// Move down to first child, if exists\r\n\t\t\tif (currentNode.childCount > 0) {\r\n\t\t\t\tnodeStack.push(currentNode);\r\n\t\t\t\tindexStack.push(currentIndex);\r\n\t\t\t\tcurrentIndex = 0;\r\n\t\t\t\tcurrentNode = currentNode.getChild(0);\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// No child nodes, so walk tree\r\n\t\t\tdo {\r\n\t\t\t\t// post-order visit\r\n\t\t\t\tif (currentNode instanceof RuleNode) {\r\n\t\t\t\t\tthis.exitRule(listener, currentNode);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// No parent, so no siblings\r\n\t\t\t\tif (nodeStack.length === 0) {\r\n\t\t\t\t\tcurrentNode = undefined;\r\n\t\t\t\t\tcurrentIndex = 0;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Move to next sibling if possible\r\n\t\t\t\tlet last = nodeStack[nodeStack.length - 1];\r\n\t\t\t\tcurrentIndex++;\r\n\t\t\t\tcurrentNode = currentIndex < last.childCount ? last.getChild(currentIndex) : undefined;\r\n\t\t\t\tif (currentNode) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// No next sibling, so move up\r\n\t\t\t\tcurrentNode = nodeStack.pop();\r\n\t\t\t\tcurrentIndex = indexStack.pop()!;\r\n\t\t\t} while (currentNode);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Enters a grammar rule by first triggering the generic event {@link ParseTreeListener#enterEveryRule}\r\n\t * then by triggering the event specific to the given parse tree node\r\n\t * @param listener The listener responding to the trigger events\r\n\t * @param r The grammar rule containing the rule context\r\n\t */\r\n\tprotected enterRule(listener: ParseTreeListener, r: RuleNode): void {\r\n\t\tlet ctx = r.ruleContext as ParserRuleContext;\r\n\t\tif (listener.enterEveryRule) {\r\n\t\t\tlistener.enterEveryRule(ctx);\r\n\t\t}\r\n\r\n\t\tctx.enterRule(listener);\r\n\t}\r\n\r\n\t/**\r\n\t * Exits a grammar rule by first triggering the event specific to the given parse tree node\r\n\t * then by triggering the generic event {@link ParseTreeListener#exitEveryRule}\r\n\t * @param listener The listener responding to the trigger events\r\n\t * @param r The grammar rule containing the rule context\r\n\t */\r\n\tprotected exitRule(listener: ParseTreeListener, r: RuleNode): void {\r\n\t\tlet ctx = r.ruleContext as ParserRuleContext;\r\n\t\tctx.exitRule(listener);\r\n\t\tif (listener.exitEveryRule) {\r\n\t\t\tlistener.exitEveryRule(ctx);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport namespace ParseTreeWalker {\r\n\texport const DEFAULT: ParseTreeWalker = new ParseTreeWalker();\r\n}\r\n"]}