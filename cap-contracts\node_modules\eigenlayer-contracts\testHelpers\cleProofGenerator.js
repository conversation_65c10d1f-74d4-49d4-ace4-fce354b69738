const MerkleTree = require('merkletreejs');
const keccak256 = require('keccak256');

const leaves = ['0x12341234123412341234123412341234123412340000000000000000000000000000000000000000000000000000000000000064', 
                '0x43214321432143214321432143214321432143210000000000000000000000000000000000000000000000000000000000000010',
                '0x12341234123412341234123412341234123412350000000000000000000000000000000000000000000000000000000000000064', 
                '0x43214321432143214321432143214321432143220000000000000000000000000000000000000000000000000000000000000010',
                '0x12341234123412341234123412341234123412360000000000000000000000000000000000000000000000000000000000000064', 
                '0x43214321432143214321432143214321432143230000000000000000000000000000000000000000000000000000000000000010',
                '0x12341234123412341234123412341234123412370000000000000000000000000000000000000000000000000000000000000064', 
                '0x43214321432143214321432143214321432143240000000000000000000000000000000000000000000000000000000000000010',].
                map(value => keccak256(value))

var tree = new MerkleTree.MerkleTree(leaves, keccak256, { sortPairs: true })

const root = tree.getHexRoot()
const proof = tree.getHexProof(leaves[2])

console.log(root, proof, "0x" + leaves[2].toString('hex'))