"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildSite = exports.DOC_ITEM_CONTEXT = exports.pageAssigner = void 0;
const path_1 = __importDefault(require("path"));
const utils_1 = require("solidity-ast/utils");
const doc_item_1 = require("./doc-item");
const clone_1 = require("./utils/clone");
const is_child_1 = require("./utils/is-child");
const memoized_getter_1 = require("./utils/memoized-getter");
exports.pageAssigner = {
    single: (_1, _2, { pageExtension: ext }) => 'index' + ext,
    items: (item, _, { pageExtension: ext }) => item.name + ext,
    files: (_, file, { pageExtension: ext, sourcesDir }) => path_1.default.relative(sourcesDir, file.absolutePath).replace('.sol', ext),
};
exports.DOC_ITEM_CONTEXT = '__item_context';
function buildSite(builds, siteConfig, properties = {}) {
    const assign = typeof siteConfig.pages === 'string' ? exports.pageAssigner[siteConfig.pages] : siteConfig.pages;
    const seen = new Set();
    const items = [];
    const pages = {};
    for (let { input, output } of builds) {
        // Clone because we will mutate in order to add item context.
        output = { ...output, sources: (0, clone_1.clone)(output.sources) };
        const deref = (0, utils_1.astDereferencer)(output);
        const decodeSrc = (0, utils_1.srcDecoder)(input, output);
        const build = { input, output, deref, decodeSrc };
        for (const { ast } of Object.values(output.sources)) {
            const isNewFile = !seen.has(ast.absolutePath);
            seen.add(ast.absolutePath);
            const relativePath = path_1.default.relative(siteConfig.sourcesDir, ast.absolutePath);
            const file = Object.assign(ast, { relativePath });
            for (const topLevelItem of file.nodes) {
                if (!(0, doc_item_1.isDocItem)(topLevelItem))
                    continue;
                const page = assignIfIncludedSource(assign, topLevelItem, file, siteConfig);
                const withContext = defineContext(topLevelItem, build, file, page);
                defineProperties(withContext, properties);
                if (isNewFile && page !== undefined) {
                    (pages[page] ?? (pages[page] = [])).push(withContext);
                    items.push(withContext);
                }
                if (!(0, utils_1.isNodeType)('ContractDefinition', topLevelItem)) {
                    continue;
                }
                for (const item of topLevelItem.nodes) {
                    if (!(0, doc_item_1.isDocItem)(item))
                        continue;
                    if (isNewFile && page !== undefined)
                        items.push(item);
                    const contract = topLevelItem.nodeType === 'ContractDefinition' ? topLevelItem : undefined;
                    const withContext = defineContext(item, build, file, page, contract);
                    defineProperties(withContext, properties);
                }
            }
        }
    }
    return {
        items,
        pages: Object.entries(pages).map(([id, pageItems]) => ({ id, items: pageItems })),
    };
}
exports.buildSite = buildSite;
function defineContext(item, build, file, page, contract) {
    return Object.assign(item, {
        [exports.DOC_ITEM_CONTEXT]: { build, file, contract, page, item: item },
    });
}
function defineProperties(item, properties) {
    for (const [prop, fn] of Object.entries(properties)) {
        const original = item[prop];
        (0, memoized_getter_1.defineGetterMemoized)(item, prop, () => fn(item.__item_context, original));
    }
}
function assignIfIncludedSource(assign, item, file, config) {
    return isFileIncluded(file.absolutePath, config)
        ? assign(item, file, config)
        : undefined;
}
function isFileIncluded(file, config) {
    return ((0, is_child_1.isChild)(file, config.sourcesDir) &&
        config.exclude.every(e => !(0, is_child_1.isChild)(file, path_1.default.join(config.sourcesDir, e))));
}
//# sourceMappingURL=site.js.map