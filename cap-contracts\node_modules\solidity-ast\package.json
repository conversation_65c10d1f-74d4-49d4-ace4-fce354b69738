{"name": "solidity-ast", "version": "0.4.59", "description": "Solidity AST schema and type definitions", "author": "<PERSON> <<EMAIL>>", "repository": "github:OpenZeppelin/solidity-ast", "types": "types.d.ts", "files": ["/finder.json", "/*.d.ts", "/utils.js", "/utils", "/dist/**/*.{js,d.ts}{,.map}", "/src/**/*.ts", "/schema.json"], "scripts": {"build:docs": "typedoc --tsconfig tsconfig.docs.json", "build:schema": "node scripts/build-schema.js", "build:types": "node scripts/build-types.js", "build:node-type": "node scripts/build-node-type.js", "build:finder": "node scripts/build-finder.js", "test": "mocha", "test:watch": "mocha -w", "prepare": "bash -x scripts/prepare.sh"}, "devDependencies": {"@types/lodash": "^4.17.7", "@types/node": "^18.14.10", "ajv": "^8.17.1", "chalk": "^4.1.2", "fast-check": "^3.20.0", "json-schema-to-typescript": "^14.1.0", "lodash": "^4.17.21", "mocha": "^10.6.0", "promisified": "^0.5.0", "semver": "^7.6.2", "solc-0.6.10": "npm:solc@0.6.10", "solc-0.6.11": "npm:solc@0.6.11", "solc-0.6.12": "npm:solc@0.6.12", "solc-0.6.6": "npm:solc@0.6.6", "solc-0.6.7": "npm:solc@0.6.7", "solc-0.6.8": "npm:solc@0.6.8", "solc-0.6.9": "npm:solc@0.6.9", "solc-0.7.0": "npm:solc@0.7.0", "solc-0.7.1": "npm:solc@0.7.1", "solc-0.7.2": "npm:solc@0.7.2", "solc-0.7.3": "npm:solc@0.7.3", "solc-0.7.4": "npm:solc@0.7.4", "solc-0.7.5": "npm:solc@0.7.5", "solc-0.7.6": "npm:solc@0.7.6", "solc-0.8.0": "npm:solc@0.8.0", "solc-0.8.10": "npm:solc@0.8.10", "solc-0.8.11": "npm:solc@0.8.11", "solc-0.8.12": "npm:solc@0.8.12", "solc-0.8.13": "npm:solc@0.8.13", "solc-0.8.15": "npm:solc@0.8.15", "solc-0.8.16": "npm:solc@0.8.16", "solc-0.8.17": "npm:solc@0.8.17", "solc-0.8.18": "npm:solc@0.8.18", "solc-0.8.19": "npm:solc@0.8.19", "solc-0.8.2": "npm:solc@0.8.2", "solc-0.8.20": "npm:solc@0.8.20", "solc-0.8.21": "npm:solc@0.8.21", "solc-0.8.22": "npm:solc@0.8.22", "solc-0.8.23": "npm:solc@0.8.23", "solc-0.8.24": "npm:solc@0.8.24", "solc-0.8.25": "npm:solc@0.8.25", "solc-0.8.26": "npm:solc@0.8.26", "solc-0.8.27": "npm:solc@0.8.27", "solc-0.8.3": "npm:solc@0.8.3", "solc-0.8.4": "npm:solc@0.8.4", "solc-0.8.5": "npm:solc@0.8.5", "solc-0.8.6": "npm:solc@0.8.6", "solc-0.8.8": "npm:solc@0.8.8", "solc-0.8.9": "npm:solc@0.8.9", "typedoc": "^0.26.4", "typedoc-theme-hierarchy": "^5.0.3", "typescript": "^5.5.3"}, "license": "MIT"}