{"17000": {"factories": {"vaultFactory": "******************************************", "delegatorFactory": "******************************************", "slasherFactory": "******************************************", "defaultStakerRewardsFactory": "0x698C36DE44D73AEfa3F0Ce3c0255A8667bdE7cFD", "defaultOperatorRewardsFactory": "0x00055dee9933F578340db42AA978b9c8B25640f6", "burnerRouterFactory": "******************************************"}, "registries": {"networkRegistry": "******************************************", "vaultRegistry": "******************************************", "operatorRegistry": "******************************************"}, "services": {"networkMetadataService": "******************************************", "networkMiddlewareService": "******************************************", "operatorMetadataService": "******************************************", "vaultOptInService": "******************************************", "networkOptInService": "******************************************", "vaultConfigurator": "******************************************"}, "vaults": {"******************************************": {"assetName": "wstETH", "asset": "******************************************", "vault": "******************************************", "curator": "******************************************", "delegator": "******************************************", "slasher": "******************************************"}}}, "11155111": {"factories": {"vaultFactory": "******************************************", "delegatorFactory": "******************************************", "slasherFactory": "******************************************", "defaultStakerRewardsFactory": "******************************************", "defaultOperatorRewardsFactory": "******************************************", "burnerRouterFactory": "******************************************"}, "registries": {"networkRegistry": "******************************************", "vaultRegistry": "******************************************", "operatorRegistry": "******************************************"}, "services": {"networkMetadataService": "******************************************", "networkMiddlewareService": "******************************************", "operatorMetadataService": "******************************************", "vaultOptInService": "******************************************", "networkOptInService": "******************************************", "vaultConfigurator": "******************************************"}, "vaults": {"******************************************": {"assetName": "wstETH", "asset": "******************************************", "vault": "******************************************", "curator": "******************************************", "delegator": "******************************************", "slasher": "******************************************"}}}}