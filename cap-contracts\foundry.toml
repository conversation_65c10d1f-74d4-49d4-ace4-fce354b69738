[profile.default]
src = "contracts"
out = "out"
test = "test"
libs = ["node_modules", "lib"]
script = "scripts"
solc_version = "0.8.28"
ignored_warnings_from = ["node_modules", "lib"]
remappings = [
    "@symbioticfi/core/=node_modules/@symbioticfi/core/",
    "@symbioticfi/burners/=node_modules/@symbioticfi/burners/",
    "@symbioticfi/hooks/=node_modules/@symbioticfi/hooks/",
    "@symbioticfi/rewards/=node_modules/@symbioticfi/rewards/",
    "@layerzerolabs/oft-evm/=lib/layerzero-devtools/packages/oft-evm/",
    "@layerzerolabs/oft-evm-upgradeable/=lib/layerzero-devtools/packages/oft-evm-upgradeable/",
    "@layerzerolabs/oapp-evm/=lib/layerzero-devtools/packages/oapp-evm/",
    "@layerzerolabs/oapp-evm-upgradeable/=lib/layerzero-devtools/packages/oapp-evm-upgradeable/",
    "@layerzerolabs/lz-evm-protocol-v2/=lib/layerzero-v2/packages/layerzero-v2/evm/protocol/",
    "@layerzerolabs/interfaces/=lib/layerzero-v2/packages/layerzero-v2/evm/protocol/contracts/interfaces/",
    "@layerzerolabs/lz-evm-messagelib-v2=lib/layerzero-v2/packages/layerzero-v2/evm/messagelib/",
    "@layerzerolabs/test-devtools-evm-foundry/=lib/layerzero-devtools/packages/test-devtools-evm-foundry/",
    "@layerzerolabs/lz-evm-v1-0.7/=lib/layerzero-v1/",
    "eigenlayer-contracts/=node_modules/eigenlayer-contracts/",
    "eigenlayer/=node_modules/eigenlayer/",
    "solidity-bytes-utils/contracts/=lib/solidity-bytes-utils/contracts/",
    "ds-test/=lib/layerzero-v2/lib/forge-std/lib/ds-test/src/",
    "forge-std/=lib/forge-std/src/",
    "@openzeppelin/=node_modules/@openzeppelin/",
]
fs_permissions = [
    { access = "read", path = "./config/symbiotic.json" },
    { access = "read", path = "./config/layerzero-v2-deployments.json" },
    { access = "read", path = "./config/zap.json" },
    { access = "read-write", path = "./config/cap-infra.json" },
    { access = "read-write", path = "./config/cap-symbiotic.json" },
    { access = "read-write", path = "./config/cap-vaults-1.json" },
    { access = "read-write", path = "./config/cap-vaults-11155111.json" },
    { access = "read-write", path = "./config/cap-vaults-17000.json" },
    { access = "read-write", path = "./config/cap-l2tokens-421614.json" },
    { access = "read-write", path = "./config/cap-symbiotic-vaults-11155111.json" },
    { access = "read-write", path = "./config/cap-symbiotic-vaults-17000.json" },
]
optimizer = true
optimizer_runs = 200

[invariant]
show_metrics = true
fail_on_revert = true
dictionary_weight = 80
[fuzz]
dictionary_weight = 60
include_storage = true
show_metrics = true

[fmt]
bracket_spacing = true
int_types = "preserve"
sort_imports = true
single_line_statement_blocks = "preserve"
contract_new_lines = false

[profile.deep]
inherit = "default"
[profile.deep.invariant]
runs = 5000
depth = 200 # handler based testing doesn't need to be deep
[profile.deep.fuzz]
runs = 5000

[profile.release]
inherits = "default"
gas_reports = ["*"]
gas_reports_ignore = [
    "MockAaveDataProvider",
    "MockChainlinkPriceFeed",
    "MockDelegation",
    "MockERC20",
    "MockOFT",
]
optimizer = true
optimizer_runs = 200
via_ir = true
use_literal_content = true
[profile.release.optimizer_details]
# # https://docs.soliditylang.org/en/v0.8.28/using-the-compiler.html#input-description
peephole = true                               # default true
jumpdestRemover = true                        # default true
simpleCounterForLoopUncheckedIncrement = true # default true
yul = true                                    # default true
# inliner = true                                # default false
# orderLiterals = true # default false
# deduplicate = true # default false
# cse = true # default false
# constantOptimizer = true # default false 

[profile.slither]
inherit = "default"
via_ir = true
