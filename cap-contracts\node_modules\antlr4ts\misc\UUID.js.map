{"version": 3, "file": "UUID.js", "sourceRoot": "", "sources": ["../../../src/misc/UUID.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,6CAA0C;AAE1C,MAAa,IAAI;IAGhB,YAAY,WAAmB,EAAE,WAAmB,EAAE,WAAmB,EAAE,YAAoB;QAC9F,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;IAC7B,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,IAAY;QACpC,IAAI,CAAC,+EAA+E,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAChG,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC9C;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5C,IAAI,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxF,IAAI,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrG,IAAI,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC;IAEM,QAAQ;QACd,OAAO,uBAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;IAEM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,IAAI,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;eAC/B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;eAC5B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;eAC5B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAEM,QAAQ;QACd,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;cACvD,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;cAC9D,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;cACrD,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;cAC9D,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;CACD;AAjDD,oBAiDC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport { Equatable } from \"./Stubs\";\r\nimport { MurmurHash } from \"./MurmurHash\";\r\n\r\nexport class UUID implements Equatable {\r\n\tprivate readonly data: Uint32Array;\r\n\r\n\tconstructor(mostSigBits: number, moreSigBits: number, lessSigBits: number, leastSigBits: number) {\r\n\t\tthis.data = new Uint32Array(4);\r\n\t\tthis.data[0] = mostSigBits;\r\n\t\tthis.data[1] = moreSigBits;\r\n\t\tthis.data[2] = lessSigBits;\r\n\t\tthis.data[3] = leastSigBits;\r\n\t}\r\n\r\n\tpublic static fromString(data: string): UUID {\r\n\t\tif (!/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/.test(data)) {\r\n\t\t\tthrow new Error(\"Incorrectly formatted UUID\");\r\n\t\t}\r\n\r\n\t\tlet segments = data.split(\"-\");\r\n\t\tlet mostSigBits = parseInt(segments[0], 16);\r\n\t\tlet moreSigBits = ((parseInt(segments[1], 16) << 16) >>> 0) + parseInt(segments[2], 16);\r\n\t\tlet lessSigBits = ((parseInt(segments[3], 16) << 16) >>> 0) + parseInt(segments[4].substr(0, 4), 16);\r\n\t\tlet leastSigBits = parseInt(segments[4].substr(-8), 16);\r\n\t\treturn new UUID(mostSigBits, moreSigBits, lessSigBits, leastSigBits);\r\n\t}\r\n\r\n\tpublic hashCode(): number {\r\n\t\treturn MurmurHash.hashCode([this.data[0], this.data[1], this.data[2], this.data[3]]);\r\n\t}\r\n\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof UUID)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.data[0] === obj.data[0]\r\n\t\t\t&& this.data[1] === obj.data[1]\r\n\t\t\t&& this.data[2] === obj.data[2]\r\n\t\t\t&& this.data[3] === obj.data[3];\r\n\t}\r\n\r\n\tpublic toString(): string {\r\n\t\treturn (\"00000000\" + this.data[0].toString(16)).substr(-8)\r\n\t\t\t+ \"-\" + (\"0000\" + (this.data[1] >>> 16).toString(16)).substr(-4)\r\n\t\t\t+ \"-\" + (\"0000\" + this.data[1].toString(16)).substr(-4)\r\n\t\t\t+ \"-\" + (\"0000\" + (this.data[2] >>> 16).toString(16)).substr(-4)\r\n\t\t\t+ \"-\" + (\"0000\" + this.data[2].toString(16)).substr(-4)\r\n\t\t\t+ (\"00000000\" + this.data[3].toString(16)).substr(-8);\r\n\t}\r\n}\r\n"]}