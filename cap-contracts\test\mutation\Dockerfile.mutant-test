FROM ghcr.io/foundry-rs/foundry:latest

# Set working directory and create it with proper permissions
WORKDIR /app
RUN chown -R foundry:foundry /app

USER foundry

# Copy the entire project
COPY --chown=foundry:foundry . .

RUN whoami
# Copy foundry cache for faster testing
RUN mkdir -p ~/.foundry/cache
RUN --mount=from=foundry_cache,target=/foundry_cache cp -r /foundry_cache/* ~/.foundry/cache

# Install dependencies and build
RUN forge build

# Warm up foundry cache by running tests once
# Invariants are too slow to run on every mutant
RUN forge test --no-match-path 'test/**/*.invariants.t.sol'

# Create an entrypoint script that will be used to test a specific mutant

ENTRYPOINT ["./test/mutation/test_mutant_docker_entrypoint.sh"]
