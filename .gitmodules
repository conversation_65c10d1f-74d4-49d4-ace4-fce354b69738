[submodule "cap-contracts/lib/forge-std"]
	path = cap-contracts/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "cap-contracts/lib/layerzero-devtools"]
	path = cap-contracts/lib/layerzero-devtools
	url = https://github.com/LayerZero-Labs/devtools
[submodule "cap-contracts/lib/layerzero-v2"]
	path = cap-contracts/lib/layerzero-v2
	url = https://github.com/LayerZero-Labs/layerzero-v2
[submodule "cap-contracts/lib/solidity-bytes-utils"]
	path = cap-contracts/lib/solidity-bytes-utils
	url = https://github.com/GNSPS/solidity-bytes-utils
[submodule "cap-contracts/lib/openzeppelin-foundry-upgrades"]
	path = cap-contracts/lib/openzeppelin-foundry-upgrades
	url = https://github.com/OpenZeppelin/openzeppelin-foundry-upgrades
[submodule "cap-contracts/lib/layerzero-v1"]
	path = cap-contracts/lib/layerzero-v1
	url = https://github.com/LayerZero-Labs/LayerZero-v1
