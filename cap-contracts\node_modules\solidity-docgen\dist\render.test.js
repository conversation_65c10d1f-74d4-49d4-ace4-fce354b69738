"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = __importDefault(require("./utils/test"));
const path_1 = __importDefault(require("path"));
const site_1 = require("./site");
const render_1 = require("./render");
/**
 * @param contracts The name of the Solidity file whose contents should be considered.
 */
function testRender(title, file, spec, expected) {
    const id = 'index.md';
    const cfg = {
        sourcesDir: 'test-contracts',
        exclude: [],
        pageExtension: '.md',
        pages: (_, f) => path_1.default.parse(f.absolutePath).name === file ? id : undefined,
    };
    (0, test_1.default)(title, t => {
        const site = (0, site_1.buildSite)(t.context.build, cfg);
        const rendered = (0, render_1.render)(site, spec, spec.collapseNewlines);
        t.is(rendered.length, 1);
        t.is(rendered[0].contents, expected);
    });
}
testRender('static page', 'S08_AB', { partials: { page: () => 'a page' } }, 'a page');
testRender('items', 'S08_AB', { partials: { page: () => '{{#each items}}{{name}}, {{/each}}' } }, 'A, B, ');
testRender('partials', 'S08_AB', {
    partials: {
        page: () => '{{#each items}}{{>part}}, {{/each}}',
        part: () => '{{name}}',
    },
}, 'A, B, ');
testRender('item partial', 'S08_AB', {
    partials: {
        page: () => '{{#each items}}{{>item}}, {{/each}}',
        contract: () => '{{name}}',
    },
}, 'A, B, ');
//# sourceMappingURL=render.test.js.map