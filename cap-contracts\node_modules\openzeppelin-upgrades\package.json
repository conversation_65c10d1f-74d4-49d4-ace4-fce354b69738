{"public": true, "name": "openzeppelin-upgrades", "description": "Secure Smart Contract library for Solidity", "version": "4.7.0", "files": ["/contracts/**/*.sol", "/build/contracts/*.json", "!/contracts/mocks/**/*"], "bin": {"openzeppelin-contracts-migrate-imports": "scripts/migrate-imports.js"}, "scripts": {"compile": "hardhat compile", "coverage": "env COVERAGE=true hardhat coverage", "docs": "oz-docs", "docs:watch": "npm run docs watch contracts 'docs/*.hbs' docs/helpers.js", "prepare-docs": "scripts/prepare-docs.sh", "lint": "npm run lint:js && npm run lint:sol", "lint:fix": "npm run lint:js:fix && npm run lint:sol:fix", "lint:js": "eslint --ignore-path .gitignore .", "lint:js:fix": "eslint --ignore-path .gitignore . --fix", "lint:sol": "solhint 'contracts/**/*.sol' && prettier -c 'contracts/**/*.sol'", "lint:sol:fix": "prettier --write \"contracts/**/*.sol\"", "clean": "hardhat clean && rimraf build contracts/build", "prepare": "scripts/prepare.sh", "prepack": "scripts/prepack.sh", "generate": "scripts/generate/run.js", "release": "scripts/release/release.sh", "version": "scripts/release/version.sh", "test": "hardhat test", "test:inheritance": "scripts/checks/inheritance-ordering.js artifacts/build-info/*", "test:generation": "scripts/checks/generation.sh", "gas-report": "env ENABLE_GAS_REPORT=true npm run test", "slither": "npm run clean && slither . --detect reentrancy-eth,reentrancy-no-eth,reentrancy-unlimited-gas"}, "repository": {"type": "git", "url": "git+https://github.com/OpenZeppelin/openzeppelin-contracts.git"}, "keywords": ["solidity", "ethereum", "smart", "contracts", "security", "zeppelin"], "author": "OpenZeppelin Community <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/OpenZeppelin/openzeppelin-contracts/issues"}, "homepage": "https://openzeppelin.com/contracts/", "devDependencies": {"@nomiclabs/hardhat-truffle5": "^2.0.5", "@nomiclabs/hardhat-web3": "^2.0.0", "@openzeppelin/docs-utils": "^0.1.0", "@openzeppelin/test-helpers": "^0.5.13", "chai": "^4.2.0", "eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.4", "eslint-plugin-mocha": "^10.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eth-sig-util": "^3.0.0", "ethereumjs-util": "^7.0.7", "ethereumjs-wallet": "^1.0.1", "graphlib": "^2.1.8", "hardhat": "^2.9.1", "hardhat-gas-reporter": "^1.0.4", "keccak256": "^1.0.2", "lodash.startcase": "^4.4.0", "lodash.zip": "^4.2.0", "merkletreejs": "^0.2.13", "micromatch": "^4.0.2", "prettier": "^2.3.0", "prettier-plugin-solidity": "^1.0.0-beta.16", "rimraf": "^3.0.2", "semver": "^7.3.5", "solhint": "^3.3.6", "solidity-ast": "^0.4.25", "solidity-coverage": "^0.7.18", "solidity-docgen": "^0.5.3", "web3": "^1.3.0", "yargs": "^17.0.0"}, "main": ".mocharc.js", "directories": {"doc": "docs", "test": "test"}}