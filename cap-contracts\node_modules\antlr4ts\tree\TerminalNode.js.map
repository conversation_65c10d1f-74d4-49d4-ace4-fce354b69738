{"version": 3, "file": "TerminalNode.js", "sourceRoot": "", "sources": ["../../../src/tree/TerminalNode.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,+CAA4C;AAC5C,8CAAyC;AAMzC,oCAAiC;AAEjC,MAAa,YAAY;IAIxB,YAAY,MAAa;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACvB,CAAC;IAGM,QAAQ,CAAC,CAAS;QACxB,MAAM,IAAI,UAAU,CAAC,gCAAgC,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAGD,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAGM,SAAS,CAAC,MAAmB;QACnC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACvB,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAGD,IAAI,cAAc;QACjB,IAAI,UAAU,GAAW,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QACjD,OAAO,IAAI,mBAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC7C,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,MAAM,CAAI,OAA4B;QAC5C,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC;IAGM,YAAY,CAAC,MAAe;QAClC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;YACpC,OAAO,OAAO,CAAC;SACf;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC;CACD;AAzDA;IADC,qBAAQ;4CAGR;AAOD;IADC,qBAAQ;0CAGR;AAGD;IADC,qBAAQ;6CAGR;AAGD;IADC,qBAAQ;2CAGR;AAGD;IADC,qBAAQ;kDAIR;AAGD;IADC,qBAAQ;8CAGR;AAGD;IADC,qBAAQ;0CAGR;AAGD;IADC,qBAAQ;wCAGR;AAGD;IADC,qBAAQ;gDAGR;AAGD;IADC,qBAAQ;4CAOR;AAjEF,oCAkEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:48.1433686-07:00\r\n\r\nimport { Interval } from \"../misc/Interval\";\r\nimport { Override } from \"../Decorators\";\r\nimport { Parser } from \"../Parser\";\r\nimport { ParseTree } from \"./ParseTree\";\r\nimport { ParseTreeVisitor } from \"./ParseTreeVisitor\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { RuleNode } from \"./RuleNode\";\r\nimport { Token } from \"../Token\";\r\n\r\nexport class TerminalNode implements ParseTree {\r\n\tpublic _symbol: Token;\r\n\tpublic _parent: RuleNode | undefined;\r\n\r\n\tconstructor(symbol: Token) {\r\n\t\tthis._symbol = symbol;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getChild(i: number): never {\r\n\t\tthrow new RangeError(\"Terminal Node has no children.\");\r\n\t}\r\n\r\n\tget symbol(): Token {\r\n\t\treturn this._symbol;\r\n\t}\r\n\r\n\t@Override\r\n\tget parent(): RuleNode | undefined {\r\n\t\treturn this._parent;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic setParent(parent: RuleContext): void {\r\n\t\tthis._parent = parent;\r\n\t}\r\n\r\n\t@Override\r\n\tget payload(): Token {\r\n\t\treturn this._symbol;\r\n\t}\r\n\r\n\t@Override\r\n\tget sourceInterval(): Interval {\r\n\t\tlet tokenIndex: number = this._symbol.tokenIndex;\r\n\t\treturn new Interval(tokenIndex, tokenIndex);\r\n\t}\r\n\r\n\t@Override\r\n\tget childCount(): number {\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic accept<T>(visitor: ParseTreeVisitor<T>): T {\r\n\t\treturn visitor.visitTerminal(this);\r\n\t}\r\n\r\n\t@Override\r\n\tget text(): string {\r\n\t\treturn this._symbol.text || \"\";\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toStringTree(parser?: Parser): string {\r\n\t\treturn this.toString();\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tif (this._symbol.type === Token.EOF) {\r\n\t\t\treturn \"<EOF>\";\r\n\t\t}\r\n\r\n\t\treturn this._symbol.text || \"\";\r\n\t}\r\n}\r\n"]}