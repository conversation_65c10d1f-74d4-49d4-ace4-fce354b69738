{"version": 3, "file": "IntervalSet.js", "sourceRoot": "", "sources": ["../../../src/misc/IntervalSet.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,uEAAoE;AACpE,+CAA4C;AAC5C,yCAAsC;AAEtC,oCAAiC;AACjC,6CAA0C;AAC1C,8CAAkD;AAClD,oCAAiC;AAGjC;;;;;;;;;;GAUG;AACH,MAAa,WAAW;IA0BvB,YAAY,SAAsB;QAF1B,aAAQ,GAAY,KAAK,CAAC;QAGjC,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACrC;aAAM;YACN,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACrB;IACF,CAAC;IA9BD,MAAM,KAAK,iBAAiB;QAC3B,IAAI,WAAW,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACjD,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAC,EAAE,CAAC,aAAK,CAAC,cAAc,EAAE,aAAK,CAAC,cAAc,CAAC,CAAC;YAC5F,WAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACjD;QAED,OAAO,WAAW,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAGD,MAAM,KAAK,SAAS;QACnB,IAAI,WAAW,CAAC,UAAU,IAAI,IAAI,EAAE;YACnC,WAAW,CAAC,UAAU,GAAG,IAAI,WAAW,EAAE,CAAC;YAC3C,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACzC;QAED,OAAO,WAAW,CAAC,UAAU,CAAC;IAC/B,CAAC;IAeD;;;OAGG;IAEI,MAAM,CAAC,EAAE,CAAC,CAAS,EAAE,IAAY,CAAC;QACxC,IAAI,CAAC,GAAgB,IAAI,WAAW,EAAE,CAAC;QACvC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,OAAO,CAAC,CAAC;IACV,CAAC;IAEM,KAAK;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACI,GAAG,CAAC,CAAS,EAAE,IAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,mBAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAED,gEAAgE;IACtD,QAAQ,CAAC,QAAkB;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACpD;QAED,kEAAkE;QAClE,IAAI,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE;YAC5B,OAAO;SACP;QAED,wBAAwB;QACxB,2CAA2C;QAC3C,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBACvB,OAAO;aACP;YAED,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAClD,oDAAoD;gBACpD,IAAI,MAAM,GAAa,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;gBAC5B,mDAAmD;gBACnD,8CAA8C;gBAC9C,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;oBACtC,CAAC,EAAE,CAAC;oBACJ,IAAI,IAAI,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;wBACpD,MAAM;qBACN;oBAED,+CAA+C;oBAC/C,kBAAkB;oBAClB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC,EAAE,CAAC;oBACJ,qCAAqC;oBACrC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACxC,uBAAuB;iBACvB;gBAED,0DAA0D;gBAC1D,OAAO;aACP;YAED,IAAI,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE;gBACrC,kBAAkB;gBAClB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACvC,OAAO;aACP;YAED,6DAA6D;SAC7D;QAED,oEAAoE;QACpE,cAAc;QACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED,4DAA4D;IACrD,MAAM,CAAC,EAAE,CAAC,IAAmB;QACnC,IAAI,CAAC,GAAgB,IAAI,WAAW,EAAE,CAAC;QACvC,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;YACnB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACZ;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,MAAM,CAAC,GAAW;QACxB,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,GAAG,YAAY,WAAW,EAAE;YAC/B,IAAI,KAAK,GAAgB,GAAG,CAAC;YAC7B,iCAAiC;YACjC,IAAI,CAAC,GAAW,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,GAAa,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACnB;SACD;aACI;YACJ,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE;gBAChC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAChB;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,eAAe,CAAC,UAAkB,EAAE,UAAkB;QAC5D,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,oBAAoB;IAEb,UAAU,CAAC,UAAkB;QACnC,IAAI,UAAU,CAAC,KAAK,EAAE;YACrB,kCAAkC;YAClC,OAAO,WAAW,CAAC,SAAS,CAAC;SAC7B;QAED,IAAI,YAAyB,CAAC;QAC9B,IAAI,UAAU,YAAY,WAAW,EAAE;YACtC,YAAY,GAAG,UAAU,CAAC;SAC1B;aAAM;YACN,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC;YACjC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SAChC;QAED,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAGM,QAAQ,CAAC,CAAS;QACxB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE;YACzB,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,YAAY,WAAW,EAAE;YAC7B,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SACrC;QAED,IAAI,KAAK,GAAgB,IAAI,WAAW,EAAE,CAAC;QAC3C,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IAEI,MAAM,CAAC,QAAQ,CAAC,IAAiB,EAAE,KAAkB;QAC3D,IAAI,IAAI,CAAC,KAAK,EAAE;YACf,OAAO,IAAI,WAAW,EAAE,CAAC;SACzB;QAED,IAAI,MAAM,GAAgB,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,KAAK,CAAC,KAAK,EAAE;YAChB,qEAAqE;YACrE,OAAO,MAAM,CAAC;SACd;QAED,IAAI,OAAO,GAAW,CAAC,CAAC;QACxB,IAAI,MAAM,GAAW,CAAC,CAAC;QACvB,OAAO,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;YAC9E,IAAI,cAAc,GAAa,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,aAAa,GAAa,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAEvD,iEAAiE;YAEjE,IAAI,aAAa,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE;gBACvC,MAAM,EAAE,CAAC;gBACT,SAAS;aACT;YAED,IAAI,aAAa,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE;gBACvC,OAAO,EAAE,CAAC;gBACV,SAAS;aACT;YAED,IAAI,aAAmC,CAAC;YACxC,IAAI,YAAkC,CAAC;YACvC,IAAI,aAAa,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE;gBACvC,aAAa,GAAG,IAAI,mBAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACpE;YAED,IAAI,aAAa,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE;gBACvC,YAAY,GAAG,IAAI,mBAAQ,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;aACnE;YAED,IAAI,aAAa,EAAE;gBAClB,IAAI,YAAY,EAAE;oBACjB,sCAAsC;oBACtC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC;oBAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;oBACvD,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;oBACT,SAAS;iBACT;qBACI;oBACJ,+BAA+B;oBAC/B,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC;oBAC3C,OAAO,EAAE,CAAC;oBACV,SAAS;iBACT;aACD;iBACI;gBACJ,IAAI,YAAY,EAAE;oBACjB,+BAA+B;oBAC/B,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC;oBAC1C,MAAM,EAAE,CAAC;oBACT,SAAS;iBACT;qBACI;oBACJ,kEAAkE;oBAClE,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBACrC,SAAS;iBACT;aACD;SACD;QAED,qFAAqF;QACrF,uFAAuF;QACvF,2BAA2B;QAC3B,OAAO,MAAM,CAAC;IACf,CAAC;IAGM,EAAE,CAAC,CAAS;QAClB,IAAI,CAAC,GAAgB,IAAI,WAAW,EAAE,CAAC;QACvC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACf,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACZ,OAAO,CAAC,CAAC;IACV,CAAC;IAED,oBAAoB;IAEb,GAAG,CAAC,KAAa;QACvB,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,wCAAwC;YAC1D,kCAAkC;YAClC,OAAO,IAAI,WAAW,EAAE,CAAC;SACzB;QAED,IAAI,WAAW,GAAe,IAAI,CAAC,UAAU,CAAC;QAC9C,IAAI,cAAc,GAAgB,KAAqB,CAAC,UAAU,CAAC;QACnE,IAAI,YAAqC,CAAC;QAC1C,IAAI,MAAM,GAAW,WAAW,CAAC,MAAM,CAAC;QACxC,IAAI,SAAS,GAAW,cAAc,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,qEAAqE;QACrE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,SAAS,EAAE;YACnC,IAAI,IAAI,GAAa,WAAW,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,MAAM,GAAa,cAAc,CAAC,CAAC,CAAC,CAAC;YACzC,yDAAyD;YACzD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;gBACtC,6DAA6D;gBAC7D,CAAC,EAAE,CAAC;aACJ;iBACI,IAAI,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;gBAC3C,8DAA8D;gBAC9D,CAAC,EAAE,CAAC;aACJ;iBACI,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;gBACvC,6CAA6C;gBAC7C,IAAI,CAAC,YAAY,EAAE;oBAClB,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC;iBACjC;gBAED,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjD,CAAC,EAAE,CAAC;aACJ;iBACI,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;gBACvC,2CAA2C;gBAC3C,IAAI,CAAC,YAAY,EAAE;oBAClB,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC;iBACjC;gBAED,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjD,CAAC,EAAE,CAAC;aACJ;iBACI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAChC,4BAA4B;gBAC5B,IAAI,CAAC,YAAY,EAAE;oBAClB,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC;iBACjC;gBAED,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjD,mDAAmD;gBACnD,+DAA+D;gBAC/D,mDAAmD;gBACnD,4DAA4D;gBAC5D,2DAA2D;gBAC3D,eAAe;gBACf,qCAAqC;gBACrC,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE;oBACxC,CAAC,EAAE,CAAC;iBACJ;qBACI,IAAI,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;oBAC7C,CAAC,EAAE,CAAC;iBACJ;aACD;SACD;QAED,IAAI,CAAC,YAAY,EAAE;YAClB,OAAO,IAAI,WAAW,EAAE,CAAC;SACzB;QAED,OAAO,YAAY,CAAC;IACrB,CAAC;IAED,oBAAoB;IAEb,QAAQ,CAAC,EAAU;QACzB,IAAI,CAAC,GAAW,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,CAAC,GAAW,CAAC,GAAG,CAAC,CAAC;QACtB,8EAA8E;QAC9E,OAAO,CAAC,IAAI,CAAC,EAAE;YACd,IAAI,CAAC,GAAW,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7B,IAAI,CAAC,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACX,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACV;iBAAM,IAAI,CAAC,GAAG,EAAE,EAAE;gBAClB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACV;iBAAM;gBACN,qBAAqB;gBACrB,OAAO,IAAI,CAAC;aACZ;SACD;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED,oBAAoB;IAEpB,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;;;OAKG;IACH,IAAI,UAAU;QACb,IAAI,IAAI,CAAC,KAAK,EAAE;YACf,MAAM,IAAI,UAAU,CAAC,cAAc,CAAC,CAAC;SACrC;QAED,IAAI,IAAI,GAAa,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,CAAC,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,IAAI,UAAU;QACb,IAAI,IAAI,CAAC,KAAK,EAAE;YACf,MAAM,IAAI,UAAU,CAAC,cAAc,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED,yCAAyC;IACzC,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC9B,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACpC;QAED,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;OAIG;IAEI,MAAM,CAAC,CAAM;QACnB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,WAAW,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SACb;QAED,OAAO,iDAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;IAC/E,CAAC;IAEM,QAAQ,CAAC,cAAuB,KAAK;QAC3C,IAAI,GAAG,GAAW,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5D,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YAClB,GAAG,IAAI,GAAG,CAAC;SACX;QAED,IAAI,KAAK,GAAY,IAAI,CAAC;QAC1B,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC9B,IAAI,KAAK,EAAE;gBACV,KAAK,GAAG,KAAK,CAAC;aACd;iBAAM;gBACN,GAAG,IAAI,IAAI,CAAC;aACZ;YAED,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,IAAI,CAAC,KAAK,aAAK,CAAC,GAAG,EAAE;oBACpB,GAAG,IAAI,OAAO,CAAC;iBACf;qBAAM,IAAI,WAAW,EAAE;oBACvB,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;iBAC3C;qBAAM;oBACN,GAAG,IAAI,CAAC,CAAC;iBACT;aACD;iBAAM;gBACN,IAAI,WAAW,EAAE;oBAChB,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;iBAC9E;qBAAM;oBACN,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;iBACpB;aACD;SACD;QAED,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YAClB,GAAG,IAAI,GAAG,CAAC;SACX;QAED,OAAO,GAAG,CAAC;IACZ,CAAC;IAEM,kBAAkB,CAAW,UAAsB;QACzD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5D,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,GAAG,GAAW,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YAClB,GAAG,IAAI,GAAG,CAAC;SACX;QAED,IAAI,KAAK,GAAY,IAAI,CAAC;QAC1B,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC9B,IAAI,KAAK,EAAE;gBACV,KAAK,GAAG,KAAK,CAAC;aACd;iBAAM;gBACN,GAAG,IAAI,IAAI,CAAC;aACZ;YAED,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;aACvC;iBAAM;gBACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC5B,IAAI,CAAC,GAAG,CAAC,EAAE;wBACV,GAAG,IAAI,IAAI,CAAC;qBACZ;oBAED,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;iBACvC;aACD;SACD;QAED,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YAClB,GAAG,IAAI,GAAG,CAAC;SACX;QAED,OAAO,GAAG,CAAC;IACZ,CAAC;IAGS,WAAW,CAAW,UAAsB,EAAE,CAAS;QAChE,IAAI,CAAC,KAAK,aAAK,CAAC,GAAG,EAAE;YACpB,OAAO,OAAO,CAAC;SACf;aAAM,IAAI,CAAC,KAAK,aAAK,CAAC,OAAO,EAAE;YAC/B,OAAO,WAAW,CAAC;SACnB;aAAM;YACN,OAAO,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SACpC;IACF,CAAC;IAGD,IAAI,IAAI;QACP,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,YAAY,GAAW,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAClD,IAAI,YAAY,KAAK,CAAC,EAAE;YACvB,IAAI,aAAa,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjD,OAAO,aAAa,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;SAC7C;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrB;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAEM,aAAa;QACnB,IAAI,MAAM,GAAgB,IAAI,yBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,GAAW,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACd;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,KAAK;QACX,IAAI,CAAC,GAAgB,IAAI,GAAG,EAAU,CAAC;QACvC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC9B,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACT;SACD;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAEM,OAAO;QACb,IAAI,MAAM,GAAa,IAAI,KAAK,EAAU,CAAC;QAC3C,IAAI,CAAC,GAAW,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACf;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGM,MAAM,CAAC,EAAU;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,GAAW,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,EAAE,GAAG,CAAC,EAAE;gBACX,MAAM,CAAC,0DAA0D;aACjE;YACD,6BAA6B;YAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;gBACzB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7B,MAAM;aACN;YACD,oCAAoC;YACpC,IAAI,EAAE,KAAK,CAAC,EAAE;gBACb,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,mBAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,MAAM;aACN;YACD,sCAAsC;YACtC,IAAI,EAAE,KAAK,CAAC,EAAE;gBACb,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,mBAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC/C,MAAM;aACN;YACD,uCAAuC;YACvC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,yBAAyB;gBAChD,IAAI,IAAI,GAAW,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,mBAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW;gBAC1D,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,eAAe;aACvC;SACD;IACF,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAEM,WAAW,CAAC,QAAiB;QACnC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;CACD;AAzgBA;IADC,qBAAQ;yCAsBR;AAQD;IADC,qBAAQ;6CAgBR;AAGD;IADC,qBAAQ;2CAaR;AAoFD;IADC,qBAAQ;qCAMR;AAID;IADC,qBAAQ;sCAyER;AAID;IADC,qBAAQ;2CAsBR;AAID;IADC,qBAAQ;wCAGR;AAqCD;IADC,qBAAQ;2CAUR;AAQD;IADC,qBAAQ;yCAOR;AA8CD;IAA4B,WAAA,oBAAO,CAAA;qDAsClC;AAGD;IADC,oBAAO;IACgB,WAAA,oBAAO,CAAA;8CAQ9B;AAGD;IADC,qBAAQ;uCAeR;AA8CD;IADC,qBAAQ;yCAoCR;AA3lBD;IADC,oBAAO;2BAKP;AA6JD;IADC,oBAAO;iCA2EP;AAlRF,kCA+oBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:40.8683480-07:00\r\n\r\nimport { ArrayEqualityComparator } from \"./ArrayEqualityComparator\";\r\nimport { IntegerList } from \"./IntegerList\";\r\nimport { Interval } from \"./Interval\";\r\nimport { IntSet } from \"./IntSet\";\r\nimport { Lexer } from \"../Lexer\";\r\nimport { MurmurHash } from \"./MurmurHash\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { Token } from \"../Token\";\r\nimport { Vocabulary } from \"../Vocabulary\";\r\n\r\n/**\r\n * This class implements the {@link IntSet} backed by a sorted array of\r\n * non-overlapping intervals. It is particularly efficient for representing\r\n * large collections of numbers, where the majority of elements appear as part\r\n * of a sequential range of numbers that are all part of the set. For example,\r\n * the set { 1, 2, 3, 4, 7, 8 } may be represented as { [1, 4], [7, 8] }.\r\n *\r\n * This class is able to represent sets containing any combination of values in\r\n * the range {@link Integer#MIN_VALUE} to {@link Integer#MAX_VALUE}\r\n * (inclusive).\r\n */\r\nexport class IntervalSet implements IntSet {\r\n\tprivate static _COMPLETE_CHAR_SET: IntervalSet;\r\n\tstatic get COMPLETE_CHAR_SET(): IntervalSet {\r\n\t\tif (IntervalSet._COMPLETE_CHAR_SET === undefined) {\r\n\t\t\tIntervalSet._COMPLETE_CHAR_SET = IntervalSet.of(Lexer.MIN_CHAR_VALUE, Lexer.MAX_CHAR_VALUE);\r\n\t\t\tIntervalSet._COMPLETE_CHAR_SET.setReadonly(true);\r\n\t\t}\r\n\r\n\t\treturn IntervalSet._COMPLETE_CHAR_SET;\r\n\t}\r\n\r\n\tprivate static _EMPTY_SET: IntervalSet;\r\n\tstatic get EMPTY_SET(): IntervalSet {\r\n\t\tif (IntervalSet._EMPTY_SET == null) {\r\n\t\t\tIntervalSet._EMPTY_SET = new IntervalSet();\r\n\t\t\tIntervalSet._EMPTY_SET.setReadonly(true);\r\n\t\t}\r\n\r\n\t\treturn IntervalSet._EMPTY_SET;\r\n\t}\r\n\r\n\t/** The list of sorted, disjoint intervals. */\r\n\tprivate _intervals: Interval[];\r\n\r\n\tprivate readonly: boolean = false;\r\n\r\n\tconstructor(intervals?: Interval[]) {\r\n\t\tif (intervals != null) {\r\n\t\t\tthis._intervals = intervals.slice(0);\r\n\t\t} else {\r\n\t\t\tthis._intervals = [];\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Create a set with all ints within range [a..b] (inclusive). If b is omitted, the set contains the single element\r\n\t * a.\r\n\t */\r\n\t@NotNull\r\n\tpublic static of(a: number, b: number = a): IntervalSet {\r\n\t\tlet s: IntervalSet = new IntervalSet();\r\n\t\ts.add(a, b);\r\n\t\treturn s;\r\n\t}\r\n\r\n\tpublic clear(): void {\r\n\t\tif (this.readonly) {\r\n\t\t\tthrow new Error(\"can't alter readonly IntervalSet\");\r\n\t\t}\r\n\r\n\t\tthis._intervals.length = 0;\r\n\t}\r\n\r\n\t/** Add interval; i.e., add all integers from a to b to set.\r\n\t *  If b&lt;a, do nothing.\r\n\t *  Keep list in sorted order (by left range value).\r\n\t *  If overlap, combine ranges.  For example,\r\n\t *  If this is {1..5, 10..20}, adding 6..7 yields\r\n\t *  {1..5, 6..7, 10..20}.  Adding 4..8 yields {1..8, 10..20}.\r\n\t */\r\n\tpublic add(a: number, b: number = a): void {\r\n\t\tthis.addRange(Interval.of(a, b));\r\n\t}\r\n\r\n\t// copy on write so we can cache a..a intervals and sets of that\r\n\tprotected addRange(addition: Interval): void {\r\n\t\tif (this.readonly) {\r\n\t\t\tthrow new Error(\"can't alter readonly IntervalSet\");\r\n\t\t}\r\n\r\n\t\t//System.out.println(\"add \"+addition+\" to \"+intervals.toString());\r\n\t\tif (addition.b < addition.a) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// find position in list\r\n\t\t// Use iterators as we modify list in place\r\n\t\tfor (let i: number = 0; i < this._intervals.length; i++) {\r\n\t\t\tlet r: Interval = this._intervals[i];\r\n\t\t\tif (addition.equals(r)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (addition.adjacent(r) || !addition.disjoint(r)) {\r\n\t\t\t\t// next to each other, make a single larger interval\r\n\t\t\t\tlet bigger: Interval = addition.union(r);\r\n\t\t\t\tthis._intervals[i] = bigger;\r\n\t\t\t\t// make sure we didn't just create an interval that\r\n\t\t\t\t// should be merged with next interval in list\r\n\t\t\t\twhile (i < this._intervals.length - 1) {\r\n\t\t\t\t\ti++;\r\n\t\t\t\t\tlet next: Interval = this._intervals[i];\r\n\t\t\t\t\tif (!bigger.adjacent(next) && bigger.disjoint(next)) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// if we bump up against or overlap next, merge\r\n\t\t\t\t\t// remove this one\r\n\t\t\t\t\tthis._intervals.splice(i, 1);\r\n\t\t\t\t\ti--;\r\n\t\t\t\t\t// move backwards to what we just set\r\n\t\t\t\t\tthis._intervals[i] = bigger.union(next);\r\n\t\t\t\t\t// set to 3 merged ones\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// first call to next after previous duplicates the result\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (addition.startsBeforeDisjoint(r)) {\r\n\t\t\t\t// insert before r\r\n\t\t\t\tthis._intervals.splice(i, 0, addition);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// if disjoint and after r, a future iteration will handle it\r\n\t\t}\r\n\r\n\t\t// ok, must be after last interval (and disjoint from last interval)\r\n\t\t// just add it\r\n\t\tthis._intervals.push(addition);\r\n\t}\r\n\r\n\t/** combine all sets in the array returned the or'd value */\r\n\tpublic static or(sets: IntervalSet[]): IntervalSet {\r\n\t\tlet r: IntervalSet = new IntervalSet();\r\n\t\tfor (let s of sets) {\r\n\t\t\tr.addAll(s);\r\n\t\t}\r\n\r\n\t\treturn r;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic addAll(set: IntSet): IntervalSet {\r\n\t\tif (set == null) {\r\n\t\t\treturn this;\r\n\t\t}\r\n\r\n\t\tif (set instanceof IntervalSet) {\r\n\t\t\tlet other: IntervalSet = set;\r\n\t\t\t// walk set and add each interval\r\n\t\t\tlet n: number = other._intervals.length;\r\n\t\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\t\tlet I: Interval = other._intervals[i];\r\n\t\t\t\tthis.add(I.a, I.b);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\tfor (let value of set.toArray()) {\r\n\t\t\t\tthis.add(value);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn this;\r\n\t}\r\n\r\n\tpublic complementRange(minElement: number, maxElement: number): IntervalSet {\r\n\t\treturn this.complement(IntervalSet.of(minElement, maxElement));\r\n\t}\r\n\r\n\t/** {@inheritDoc} */\r\n\t@Override\r\n\tpublic complement(vocabulary: IntSet): IntervalSet {\r\n\t\tif (vocabulary.isNil) {\r\n\t\t\t// nothing in common with null set\r\n\t\t\treturn IntervalSet.EMPTY_SET;\r\n\t\t}\r\n\r\n\t\tlet vocabularyIS: IntervalSet;\r\n\t\tif (vocabulary instanceof IntervalSet) {\r\n\t\t\tvocabularyIS = vocabulary;\r\n\t\t} else {\r\n\t\t\tvocabularyIS = new IntervalSet();\r\n\t\t\tvocabularyIS.addAll(vocabulary);\r\n\t\t}\r\n\r\n\t\treturn vocabularyIS.subtract(this);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic subtract(a: IntSet): IntervalSet {\r\n\t\tif (a == null || a.isNil) {\r\n\t\t\treturn new IntervalSet(this._intervals);\r\n\t\t}\r\n\r\n\t\tif (a instanceof IntervalSet) {\r\n\t\t\treturn IntervalSet.subtract(this, a);\r\n\t\t}\r\n\r\n\t\tlet other: IntervalSet = new IntervalSet();\r\n\t\tother.addAll(a);\r\n\t\treturn IntervalSet.subtract(this, other);\r\n\t}\r\n\r\n\t/**\r\n\t * Compute the set difference between two interval sets. The specific\r\n\t * operation is `left - right`.\r\n\t */\r\n\t@NotNull\r\n\tpublic static subtract(left: IntervalSet, right: IntervalSet): IntervalSet {\r\n\t\tif (left.isNil) {\r\n\t\t\treturn new IntervalSet();\r\n\t\t}\r\n\r\n\t\tlet result: IntervalSet = new IntervalSet(left._intervals);\r\n\t\tif (right.isNil) {\r\n\t\t\t// right set has no elements; just return the copy of the current set\r\n\t\t\treturn result;\r\n\t\t}\r\n\r\n\t\tlet resultI: number = 0;\r\n\t\tlet rightI: number = 0;\r\n\t\twhile (resultI < result._intervals.length && rightI < right._intervals.length) {\r\n\t\t\tlet resultInterval: Interval = result._intervals[resultI];\r\n\t\t\tlet rightInterval: Interval = right._intervals[rightI];\r\n\r\n\t\t\t// operation: (resultInterval - rightInterval) and update indexes\r\n\r\n\t\t\tif (rightInterval.b < resultInterval.a) {\r\n\t\t\t\trightI++;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (rightInterval.a > resultInterval.b) {\r\n\t\t\t\tresultI++;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet beforeCurrent: Interval | undefined;\r\n\t\t\tlet afterCurrent: Interval | undefined;\r\n\t\t\tif (rightInterval.a > resultInterval.a) {\r\n\t\t\t\tbeforeCurrent = new Interval(resultInterval.a, rightInterval.a - 1);\r\n\t\t\t}\r\n\r\n\t\t\tif (rightInterval.b < resultInterval.b) {\r\n\t\t\t\tafterCurrent = new Interval(rightInterval.b + 1, resultInterval.b);\r\n\t\t\t}\r\n\r\n\t\t\tif (beforeCurrent) {\r\n\t\t\t\tif (afterCurrent) {\r\n\t\t\t\t\t// split the current interval into two\r\n\t\t\t\t\tresult._intervals[resultI] = beforeCurrent;\r\n\t\t\t\t\tresult._intervals.splice(resultI + 1, 0, afterCurrent);\r\n\t\t\t\t\tresultI++;\r\n\t\t\t\t\trightI++;\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\t// replace the current interval\r\n\t\t\t\t\tresult._intervals[resultI] = beforeCurrent;\r\n\t\t\t\t\tresultI++;\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tif (afterCurrent) {\r\n\t\t\t\t\t// replace the current interval\r\n\t\t\t\t\tresult._intervals[resultI] = afterCurrent;\r\n\t\t\t\t\trightI++;\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\t// remove the current interval (thus no need to increment resultI)\r\n\t\t\t\t\tresult._intervals.splice(resultI, 1);\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// If rightI reached right.intervals.size, no more intervals to subtract from result.\r\n\t\t// If resultI reached result.intervals.size, we would be subtracting from an empty set.\r\n\t\t// Either way, we are done.\r\n\t\treturn result;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic or(a: IntSet): IntervalSet {\r\n\t\tlet o: IntervalSet = new IntervalSet();\r\n\t\to.addAll(this);\r\n\t\to.addAll(a);\r\n\t\treturn o;\r\n\t}\r\n\r\n\t/** {@inheritDoc} */\r\n\t@Override\r\n\tpublic and(other: IntSet): IntervalSet {\r\n\t\tif (other.isNil) { //|| !(other instanceof IntervalSet) ) {\r\n\t\t\t// nothing in common with null set\r\n\t\t\treturn new IntervalSet();\r\n\t\t}\r\n\r\n\t\tlet myIntervals: Interval[] = this._intervals;\r\n\t\tlet theirIntervals: Interval[] = (other as IntervalSet)._intervals;\r\n\t\tlet intersection: IntervalSet | undefined;\r\n\t\tlet mySize: number = myIntervals.length;\r\n\t\tlet theirSize: number = theirIntervals.length;\r\n\t\tlet i: number = 0;\r\n\t\tlet j: number = 0;\r\n\t\t// iterate down both interval lists looking for nondisjoint intervals\r\n\t\twhile (i < mySize && j < theirSize) {\r\n\t\t\tlet mine: Interval = myIntervals[i];\r\n\t\t\tlet theirs: Interval = theirIntervals[j];\r\n\t\t\t//System.out.println(\"mine=\"+mine+\" and theirs=\"+theirs);\r\n\t\t\tif (mine.startsBeforeDisjoint(theirs)) {\r\n\t\t\t\t// move this iterator looking for interval that might overlap\r\n\t\t\t\ti++;\r\n\t\t\t}\r\n\t\t\telse if (theirs.startsBeforeDisjoint(mine)) {\r\n\t\t\t\t// move other iterator looking for interval that might overlap\r\n\t\t\t\tj++;\r\n\t\t\t}\r\n\t\t\telse if (mine.properlyContains(theirs)) {\r\n\t\t\t\t// overlap, add intersection, get next theirs\r\n\t\t\t\tif (!intersection) {\r\n\t\t\t\t\tintersection = new IntervalSet();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tintersection.addRange(mine.intersection(theirs));\r\n\t\t\t\tj++;\r\n\t\t\t}\r\n\t\t\telse if (theirs.properlyContains(mine)) {\r\n\t\t\t\t// overlap, add intersection, get next mine\r\n\t\t\t\tif (!intersection) {\r\n\t\t\t\t\tintersection = new IntervalSet();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tintersection.addRange(mine.intersection(theirs));\r\n\t\t\t\ti++;\r\n\t\t\t}\r\n\t\t\telse if (!mine.disjoint(theirs)) {\r\n\t\t\t\t// overlap, add intersection\r\n\t\t\t\tif (!intersection) {\r\n\t\t\t\t\tintersection = new IntervalSet();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tintersection.addRange(mine.intersection(theirs));\r\n\t\t\t\t// Move the iterator of lower range [a..b], but not\r\n\t\t\t\t// the upper range as it may contain elements that will collide\r\n\t\t\t\t// with the next iterator. So, if mine=[0..115] and\r\n\t\t\t\t// theirs=[115..200], then intersection is 115 and move mine\r\n\t\t\t\t// but not theirs as theirs may collide with the next range\r\n\t\t\t\t// in thisIter.\r\n\t\t\t\t// move both iterators to next ranges\r\n\t\t\t\tif (mine.startsAfterNonDisjoint(theirs)) {\r\n\t\t\t\t\tj++;\r\n\t\t\t\t}\r\n\t\t\t\telse if (theirs.startsAfterNonDisjoint(mine)) {\r\n\t\t\t\t\ti++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (!intersection) {\r\n\t\t\treturn new IntervalSet();\r\n\t\t}\r\n\r\n\t\treturn intersection;\r\n\t}\r\n\r\n\t/** {@inheritDoc} */\r\n\t@Override\r\n\tpublic contains(el: number): boolean {\r\n\t\tlet n: number = this._intervals.length;\r\n\t\tlet l: number = 0;\r\n\t\tlet r: number = n - 1;\r\n\t\t// Binary search for the element in the (sorted, disjoint) array of intervals.\r\n\t\twhile (l <= r) {\r\n\t\t\tlet m: number = (l + r) >> 1;\r\n\t\t\tlet I: Interval = this._intervals[m];\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tif (b < el) {\r\n\t\t\t\tl = m + 1;\r\n\t\t\t} else if (a > el) {\r\n\t\t\t\tr = m - 1;\r\n\t\t\t} else {\r\n\t\t\t\t// el >= a && el <= b\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/** {@inheritDoc} */\r\n\t@Override\r\n\tget isNil(): boolean {\r\n\t\treturn this._intervals == null || this._intervals.length === 0;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the maximum value contained in the set if not isNil.\r\n\t *\r\n\t * @return the maximum value contained in the set.\r\n\t * @throws RangeError if set is empty\r\n\t */\r\n\tget maxElement(): number {\r\n\t\tif (this.isNil) {\r\n\t\t\tthrow new RangeError(\"set is empty\");\r\n\t\t}\r\n\r\n\t\tlet last: Interval = this._intervals[this._intervals.length - 1];\r\n\t\treturn last.b;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the minimum value contained in the set if not isNil.\r\n\t *\r\n\t * @return the minimum value contained in the set.\r\n\t * @throws RangeError if set is empty\r\n\t */\r\n\tget minElement(): number {\r\n\t\tif (this.isNil) {\r\n\t\t\tthrow new RangeError(\"set is empty\");\r\n\t\t}\r\n\r\n\t\treturn this._intervals[0].a;\r\n\t}\r\n\r\n\t/** Return a list of Interval objects. */\r\n\tget intervals(): Interval[] {\r\n\t\treturn this._intervals;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\tfor (let I of this._intervals) {\r\n\t\t\thash = MurmurHash.update(hash, I.a);\r\n\t\t\thash = MurmurHash.update(hash, I.b);\r\n\t\t}\r\n\r\n\t\thash = MurmurHash.finish(hash, this._intervals.length * 2);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\t/** Are two IntervalSets equal?  Because all intervals are sorted\r\n\t *  and disjoint, equals is a simple linear walk over both lists\r\n\t *  to make sure they are the same.  Interval.equals() is used\r\n\t *  by the List.equals() method to check the ranges.\r\n\t */\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (o == null || !(o instanceof IntervalSet)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn ArrayEqualityComparator.INSTANCE.equals(this._intervals, o._intervals);\r\n\t}\r\n\r\n\tpublic toString(elemAreChar: boolean = false): string {\r\n\t\tlet buf: string = \"\";\r\n\t\tif (this._intervals == null || this._intervals.length === 0) {\r\n\t\t\treturn \"{}\";\r\n\t\t}\r\n\r\n\t\tif (this.size > 1) {\r\n\t\t\tbuf += \"{\";\r\n\t\t}\r\n\r\n\t\tlet first: boolean = true;\r\n\t\tfor (let I of this._intervals) {\r\n\t\t\tif (first) {\r\n\t\t\t\tfirst = false;\r\n\t\t\t} else {\r\n\t\t\t\tbuf += \", \";\r\n\t\t\t}\r\n\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tif (a === b) {\r\n\t\t\t\tif (a === Token.EOF) {\r\n\t\t\t\t\tbuf += \"<EOF>\";\r\n\t\t\t\t} else if (elemAreChar) {\r\n\t\t\t\t\tbuf += \"'\" + String.fromCodePoint(a) + \"'\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tbuf += a;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (elemAreChar) {\r\n\t\t\t\t\tbuf += \"'\" + String.fromCodePoint(a) + \"'..'\" + String.fromCodePoint(b) + \"'\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tbuf += a + \"..\" + b;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (this.size > 1) {\r\n\t\t\tbuf += \"}\";\r\n\t\t}\r\n\r\n\t\treturn buf;\r\n\t}\r\n\r\n\tpublic toStringVocabulary( @NotNull vocabulary: Vocabulary): string {\r\n\t\tif (this._intervals == null || this._intervals.length === 0) {\r\n\t\t\treturn \"{}\";\r\n\t\t}\r\n\r\n\t\tlet buf: string = \"\";\r\n\t\tif (this.size > 1) {\r\n\t\t\tbuf += \"{\";\r\n\t\t}\r\n\r\n\t\tlet first: boolean = true;\r\n\t\tfor (let I of this._intervals) {\r\n\t\t\tif (first) {\r\n\t\t\t\tfirst = false;\r\n\t\t\t} else {\r\n\t\t\t\tbuf += \", \";\r\n\t\t\t}\r\n\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tif (a === b) {\r\n\t\t\t\tbuf += this.elementName(vocabulary, a);\r\n\t\t\t} else {\r\n\t\t\t\tfor (let i = a; i <= b; i++) {\r\n\t\t\t\t\tif (i > a) {\r\n\t\t\t\t\t\tbuf += \", \";\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbuf += this.elementName(vocabulary, i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (this.size > 1) {\r\n\t\t\tbuf += \"}\";\r\n\t\t}\r\n\r\n\t\treturn buf;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected elementName( @NotNull vocabulary: Vocabulary, a: number): string {\r\n\t\tif (a === Token.EOF) {\r\n\t\t\treturn \"<EOF>\";\r\n\t\t} else if (a === Token.EPSILON) {\r\n\t\t\treturn \"<EPSILON>\";\r\n\t\t} else {\r\n\t\t\treturn vocabulary.getDisplayName(a);\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\tlet n: number = 0;\r\n\t\tlet numIntervals: number = this._intervals.length;\r\n\t\tif (numIntervals === 1) {\r\n\t\t\tlet firstInterval: Interval = this._intervals[0];\r\n\t\t\treturn firstInterval.b - firstInterval.a + 1;\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < numIntervals; i++) {\r\n\t\t\tlet I: Interval = this._intervals[i];\r\n\t\t\tn += (I.b - I.a + 1);\r\n\t\t}\r\n\r\n\t\treturn n;\r\n\t}\r\n\r\n\tpublic toIntegerList(): IntegerList {\r\n\t\tlet values: IntegerList = new IntegerList(this.size);\r\n\t\tlet n: number = this._intervals.length;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tlet I: Interval = this._intervals[i];\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tfor (let v = a; v <= b; v++) {\r\n\t\t\t\tvalues.add(v);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn values;\r\n\t}\r\n\r\n\tpublic toSet(): Set<number> {\r\n\t\tlet s: Set<number> = new Set<number>();\r\n\t\tfor (let I of this._intervals) {\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tfor (let v = a; v <= b; v++) {\r\n\t\t\t\ts.add(v);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn s;\r\n\t}\r\n\r\n\tpublic toArray(): number[] {\r\n\t\tlet values: number[] = new Array<number>();\r\n\t\tlet n: number = this._intervals.length;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tlet I: Interval = this._intervals[i];\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tfor (let v = a; v <= b; v++) {\r\n\t\t\t\tvalues.push(v);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn values;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic remove(el: number): void {\r\n\t\tif (this.readonly) {\r\n\t\t\tthrow new Error(\"can't alter readonly IntervalSet\");\r\n\t\t}\r\n\r\n\t\tlet n: number = this._intervals.length;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tlet I: Interval = this._intervals[i];\r\n\t\t\tlet a: number = I.a;\r\n\t\t\tlet b: number = I.b;\r\n\t\t\tif (el < a) {\r\n\t\t\t\tbreak; // list is sorted and el is before this interval; not here\r\n\t\t\t}\r\n\t\t\t// if whole interval x..x, rm\r\n\t\t\tif (el === a && el === b) {\r\n\t\t\t\tthis._intervals.splice(i, 1);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\t// if on left edge x..b, adjust left\r\n\t\t\tif (el === a) {\r\n\t\t\t\tthis._intervals[i] = Interval.of(I.a + 1, I.b);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\t// if on right edge a..x, adjust right\r\n\t\t\tif (el === b) {\r\n\t\t\t\tthis._intervals[i] = Interval.of(I.a, I.b - 1);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\t// if in middle a..x..b, split interval\r\n\t\t\tif (el > a && el < b) { // found in this interval\r\n\t\t\t\tlet oldb: number = I.b;\r\n\t\t\t\tthis._intervals[i] = Interval.of(I.a, el - 1); // [a..x-1]\r\n\t\t\t\tthis.add(el + 1, oldb); // add [x+1..b]\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tget isReadonly(): boolean {\r\n\t\treturn this.readonly;\r\n\t}\r\n\r\n\tpublic setReadonly(readonly: boolean): void {\r\n\t\tif (this.readonly && !readonly) {\r\n\t\t\tthrow new Error(\"can't alter readonly IntervalSet\");\r\n\t\t}\r\n\r\n\t\tthis.readonly = readonly;\r\n\t}\r\n}\r\n"]}