{"version": 3, "file": "ParseTreeListener.js", "sourceRoot": "", "sources": ["../../../src/tree/ParseTreeListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.6109431-07:00\r\n\r\nimport { ErrorNode } from \"./ErrorNode\";\r\nimport { ParserRuleContext } from \"../ParserRuleContext\";\r\nimport { TerminalNode } from \"./TerminalNode\";\r\n\r\n/** This interface describes the minimal core of methods triggered\r\n *  by {@link ParseTreeWalker}. E.g.,\r\n *\r\n * ```\r\n * ParseTreeWalker walker = new ParseTreeWalker();\r\n * walker.walk(myParseTreeListener, myParseTree); <-- triggers events in your listener\r\n * ```\r\n *\r\n *  If you want to trigger events in multiple listeners during a single\r\n *  tree walk, you can use the ParseTreeDispatcher object available at\r\n *\r\n * \t\thttps://github.com/antlr/antlr4/issues/841\r\n */\r\nexport interface ParseTreeListener {\r\n\tvisitTerminal?: (/*@NotNull*/ node: TerminalNode) => void;\r\n\tvisitErrorNode?: (/*@NotNull*/ node: ErrorNode) => void;\r\n\tenterEveryRule?: (/*@NotNull*/ ctx: ParserRuleContext) => void;\r\n\texitEveryRule?: (/*@NotNull*/ ctx: ParserRuleContext) => void;\r\n}\r\n"]}