{"name": "solidity-docgen", "version": "0.6.0-beta.36", "description": "Documentation generator for Solidity smart contracts.", "repository": "github:OpenZeppelin/solidity-docgen", "keywords": ["solidity", "documentation"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/index.js", "files": ["dist", "src"], "scripts": {"build": "tsc", "clean": "hardhat clean && rimraf dist tsconfig.tsbuildinfo", "coverage": "c8 npm run test", "docs": "oz-docs -c docs", "docs:watch": "npm run docs watch", "prepare": "npm run clean && npm run build", "test": "hardhat compile && ava"}, "dependencies": {"handlebars": "^4.7.7", "solidity-ast": "^0.4.38"}, "peerDependencies": {"hardhat": "^2.8.0"}, "devDependencies": {"@types/mocha": "^7.0.2", "@types/node": "^14.18.26", "ava": "^5.0.0", "c8": "^8.0.0", "code-style": "github:OpenZeppelin/code-style", "hardhat": "^2.8.0", "openzeppelin-docs-utils": "github:OpenZeppelin/docs-utils", "ts-node": "^10.4.0", "typescript": "^4.0.0"}}