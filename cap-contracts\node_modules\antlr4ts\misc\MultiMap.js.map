{"version": 3, "file": "MultiMap.js", "sourceRoot": "", "sources": ["../../../src/misc/MultiMap.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,MAAa,QAAe,SAAQ,GAAW;IAC9C;QACC,KAAK,EAAE,CAAC;IACT,CAAC;IAEM,GAAG,CAAC,GAAM,EAAE,KAAQ;QAC1B,IAAI,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,EAAE;YACpB,cAAc,GAAG,EAAS,CAAC;YAC3B,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;SAC/B;QACD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEM,QAAQ;QACd,IAAI,KAAK,GAAkB,EAAE,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,GAAM,EAAE,EAAE;YACpC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACpB,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACd,CAAC;CACD;AAvBD,4BAuBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:42.1346951-07:00\r\n\r\nexport class MultiMap<K, V> extends Map<K, V[]> {\r\n\tconstructor() {\r\n\t\tsuper();\r\n\t}\r\n\r\n\tpublic map(key: K, value: V): void {\r\n\t\tlet elementsForKey = super.get(key);\r\n\t\tif (!elementsForKey) {\r\n\t\t\telementsForKey = [] as V[];\r\n\t\t\tsuper.set(key, elementsForKey);\r\n\t\t}\r\n\t\telementsForKey.push(value);\r\n\t}\r\n\r\n\tpublic getPairs(): Array<[K, V]> {\r\n\t\tlet pairs: Array<[K, V]> = [];\r\n\t\tthis.forEach((values: V[], key: K) => {\r\n\t\t\tvalues.forEach((v) => {\r\n\t\t\t\tpairs.push([key, v]);\r\n\t\t\t});\r\n\t\t});\r\n\t\treturn pairs;\r\n\t}\r\n}\r\n"]}