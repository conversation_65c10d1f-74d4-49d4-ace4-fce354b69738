/* tslint:disable */

export type SourceLocation = string;
export type Expression =
  | Assignment
  | BinaryOperation
  | Conditional
  | ElementaryTypeNameExpression
  | FunctionCall
  | FunctionCallOptions
  | Identifier
  | IndexAccess
  | IndexRangeAccess
  | Literal
  | MemberAccess
  | NewExpression
  | TupleExpression
  | UnaryOperation;
export type StateMutability = "payable" | "pure" | "nonpayable" | "view";
export type TypeName = ArrayTypeName | ElementaryTypeName | FunctionTypeName | Mapping | UserDefinedTypeName;
export type Mutability = "mutable" | "immutable" | "constant";
export type StorageLocation = "calldata" | "default" | "memory" | "storage" | "transient";
export type Visibility = "external" | "public" | "internal" | "private";
export type Statement =
  | Block
  | Break
  | Continue
  | DoWhileStatement
  | EmitStatement
  | ExpressionStatement
  | ForStatement
  | IfStatement
  | InlineAssembly
  | PlaceholderStatement
  | Return
  | RevertStatement
  | TryStatement
  | UncheckedBlock
  | VariableDeclarationStatement
  | WhileStatement;
export type YulStatement =
  | YulAssignment
  | YulBlock
  | YulBreak
  | YulContinue
  | YulExpressionStatement
  | YulLeave
  | YulForLoop
  | YulFunctionDefinition
  | YulIf
  | YulSwitch
  | YulVariableDeclaration;
export type YulExpression = YulFunctionCall | YulIdentifier | YulLiteral;
export type YulLiteral = YulLiteralValue | YulLiteralHexValue;

export interface SourceUnit {
  id: number;
  src: SourceLocation;
  absolutePath: string;
  exportedSymbols: {
    [k: string]: number[] | undefined;
  };
  experimentalSolidity?: boolean;
  license?: string | null;
  nodes: (
    | ContractDefinition
    | EnumDefinition
    | ErrorDefinition
    | FunctionDefinition
    | ImportDirective
    | PragmaDirective
    | StructDefinition
    | UserDefinedValueTypeDefinition
    | UsingForDirective
    | VariableDeclaration
  )[];
  nodeType: "SourceUnit";
}
export interface ContractDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  abstract: boolean;
  baseContracts: InheritanceSpecifier[];
  canonicalName?: string;
  contractDependencies: number[];
  contractKind: "contract" | "interface" | "library";
  documentation?: StructuredDocumentation | null;
  fullyImplemented: boolean;
  linearizedBaseContracts: number[];
  nodes: (
    | EnumDefinition
    | ErrorDefinition
    | EventDefinition
    | FunctionDefinition
    | ModifierDefinition
    | StructDefinition
    | UserDefinedValueTypeDefinition
    | UsingForDirective
    | VariableDeclaration
  )[];
  scope: number;
  usedErrors?: number[];
  usedEvents?: number[];
  internalFunctionIDs?: {
    [k: string]: number | undefined;
  };
  nodeType: "ContractDefinition";
}
export interface InheritanceSpecifier {
  id: number;
  src: SourceLocation;
  arguments?: Expression[] | null;
  baseName: UserDefinedTypeName | IdentifierPath;
  nodeType: "InheritanceSpecifier";
}
export interface Assignment {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  leftHandSide: Expression;
  operator: "=" | "+=" | "-=" | "*=" | "/=" | "%=" | "|=" | "&=" | "^=" | ">>=" | "<<=";
  rightHandSide: Expression;
  nodeType: "Assignment";
}
export interface TypeDescriptions {
  typeIdentifier?: string | null;
  typeString?: string | null;
}
export interface BinaryOperation {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  commonType: TypeDescriptions;
  leftExpression: Expression;
  operator:
    | "+"
    | "-"
    | "*"
    | "/"
    | "%"
    | "**"
    | "&&"
    | "||"
    | "!="
    | "=="
    | "<"
    | "<="
    | ">"
    | ">="
    | "^"
    | "&"
    | "|"
    | "<<"
    | ">>";
  rightExpression: Expression;
  function?: number;
  nodeType: "BinaryOperation";
}
export interface Conditional {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  condition: Expression;
  falseExpression: Expression;
  trueExpression: Expression;
  nodeType: "Conditional";
}
export interface ElementaryTypeNameExpression {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  typeName: ElementaryTypeName;
  nodeType: "ElementaryTypeNameExpression";
}
export interface ElementaryTypeName {
  id: number;
  src: SourceLocation;
  typeDescriptions: TypeDescriptions;
  name: string;
  stateMutability?: StateMutability;
  nodeType: "ElementaryTypeName";
}
export interface FunctionCall {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  arguments: Expression[];
  expression: Expression;
  kind: "functionCall" | "typeConversion" | "structConstructorCall";
  names: string[];
  nameLocations?: string[];
  tryCall: boolean;
  nodeType: "FunctionCall";
}
export interface FunctionCallOptions {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue?: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  expression: Expression;
  names: string[];
  options: Expression[];
  nodeType: "FunctionCallOptions";
}
export interface Identifier {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  name: string;
  overloadedDeclarations: number[];
  referencedDeclaration?: number | null;
  typeDescriptions: TypeDescriptions;
  nodeType: "Identifier";
}
export interface IndexAccess {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  baseExpression: Expression;
  indexExpression?: Expression | null;
  nodeType: "IndexAccess";
}
export interface IndexRangeAccess {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  baseExpression: Expression;
  endExpression?: Expression | null;
  startExpression?: Expression | null;
  nodeType: "IndexRangeAccess";
}
export interface Literal {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  hexValue: string;
  kind: "bool" | "number" | "string" | "hexString" | "unicodeString";
  subdenomination?:
    | ("seconds" | "minutes" | "hours" | "days" | "weeks" | "wei" | "gwei" | "ether" | "finney" | "szabo")
    | null;
  value?: string | null;
  nodeType: "Literal";
}
export interface MemberAccess {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  expression: Expression;
  memberName: string;
  memberLocation?: string;
  referencedDeclaration?: number | null;
  nodeType: "MemberAccess";
}
export interface NewExpression {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue?: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  typeName: TypeName;
  nodeType: "NewExpression";
}
export interface ArrayTypeName {
  id: number;
  src: SourceLocation;
  typeDescriptions: TypeDescriptions;
  baseType: TypeName;
  length?: Expression | null;
  nodeType: "ArrayTypeName";
}
export interface FunctionTypeName {
  id: number;
  src: SourceLocation;
  typeDescriptions: TypeDescriptions;
  parameterTypes: ParameterList;
  returnParameterTypes: ParameterList;
  stateMutability: StateMutability;
  visibility: Visibility;
  nodeType: "FunctionTypeName";
}
export interface ParameterList {
  id: number;
  src: SourceLocation;
  parameters: VariableDeclaration[];
  nodeType: "ParameterList";
}
export interface VariableDeclaration {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  baseFunctions?: number[] | null;
  constant: boolean;
  documentation?: StructuredDocumentation | null;
  functionSelector?: string;
  indexed?: boolean;
  mutability: Mutability;
  overrides?: OverrideSpecifier | null;
  scope: number;
  stateVariable: boolean;
  storageLocation: StorageLocation;
  typeDescriptions: TypeDescriptions;
  typeName?: TypeName | null;
  value?: Expression | null;
  visibility: Visibility;
  nodeType: "VariableDeclaration";
}
export interface StructuredDocumentation {
  id: number;
  src: SourceLocation;
  text: string;
  nodeType: "StructuredDocumentation";
}
export interface OverrideSpecifier {
  id: number;
  src: SourceLocation;
  overrides: UserDefinedTypeName[] | IdentifierPath[];
  nodeType: "OverrideSpecifier";
}
export interface UserDefinedTypeName {
  id: number;
  src: SourceLocation;
  typeDescriptions: TypeDescriptions;
  contractScope?: null;
  name?: string;
  pathNode?: IdentifierPath;
  referencedDeclaration: number;
  nodeType: "UserDefinedTypeName";
}
export interface IdentifierPath {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocations?: string[];
  referencedDeclaration: number;
  nodeType: "IdentifierPath";
}
export interface Mapping {
  id: number;
  src: SourceLocation;
  typeDescriptions: TypeDescriptions;
  keyType: TypeName;
  valueType: TypeName;
  keyName?: string;
  keyNameLocation?: string;
  valueName?: string;
  valueNameLocation?: string;
  nodeType: "Mapping";
}
export interface TupleExpression {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  components: (Expression | null)[];
  isInlineArray: boolean;
  nodeType: "TupleExpression";
}
export interface UnaryOperation {
  id: number;
  src: SourceLocation;
  argumentTypes?: TypeDescriptions[] | null;
  isConstant: boolean;
  isLValue: boolean;
  isPure: boolean;
  lValueRequested: boolean;
  typeDescriptions: TypeDescriptions;
  operator: "++" | "--" | "-" | "!" | "delete" | "~";
  prefix: boolean;
  subExpression: Expression;
  function?: number;
  nodeType: "UnaryOperation";
}
export interface EnumDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  canonicalName: string;
  members: EnumValue[];
  documentation?: StructuredDocumentation | null;
  nodeType: "EnumDefinition";
}
export interface EnumValue {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  nodeType: "EnumValue";
}
export interface ErrorDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation: string;
  documentation?: StructuredDocumentation | null;
  errorSelector?: string;
  parameters: ParameterList;
  nodeType: "ErrorDefinition";
}
export interface EventDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  anonymous: boolean;
  eventSelector?: string;
  documentation?: StructuredDocumentation | null;
  parameters: ParameterList;
  nodeType: "EventDefinition";
}
export interface FunctionDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  baseFunctions?: number[];
  body?: Block | null;
  documentation?: StructuredDocumentation | null;
  functionSelector?: string;
  implemented: boolean;
  kind: "function" | "receive" | "constructor" | "fallback" | "freeFunction";
  modifiers: ModifierInvocation[];
  overrides?: OverrideSpecifier | null;
  parameters: ParameterList;
  returnParameters: ParameterList;
  scope: number;
  stateMutability: StateMutability;
  virtual: boolean;
  visibility: Visibility;
  nodeType: "FunctionDefinition";
}
export interface Block {
  id: number;
  src: SourceLocation;
  documentation?: string;
  statements?: Statement[] | null;
  nodeType: "Block";
}
export interface Break {
  id: number;
  src: SourceLocation;
  documentation?: string;
  nodeType: "Break";
}
export interface Continue {
  id: number;
  src: SourceLocation;
  documentation?: string;
  nodeType: "Continue";
}
export interface DoWhileStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  body: Block | Statement;
  condition: Expression;
  nodeType: "DoWhileStatement";
}
export interface EmitStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  eventCall: FunctionCall;
  nodeType: "EmitStatement";
}
export interface ExpressionStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  expression: Expression;
  nodeType: "ExpressionStatement";
}
export interface ForStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  body: Block | Statement;
  condition?: Expression | null;
  initializationExpression?: (ExpressionStatement | VariableDeclarationStatement) | null;
  loopExpression?: ExpressionStatement | null;
  isSimpleCounterLoop?: boolean;
  nodeType: "ForStatement";
}
export interface VariableDeclarationStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  assignments: (number | null)[];
  declarations: (VariableDeclaration | null)[];
  initialValue?: Expression | null;
  nodeType: "VariableDeclarationStatement";
}
export interface IfStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  condition: Expression;
  falseBody?: (Statement | Block) | null;
  trueBody: Statement | Block;
  nodeType: "IfStatement";
}
export interface InlineAssembly {
  id: number;
  src: SourceLocation;
  documentation?: string;
  AST: YulBlock;
  evmVersion:
    | "homestead"
    | "tangerineWhistle"
    | "spuriousDragon"
    | "byzantium"
    | "constantinople"
    | "petersburg"
    | "istanbul"
    | "berlin"
    | "london"
    | "paris"
    | "shanghai"
    | "cancun"
    | "prague";
  externalReferences: {
    declaration: number;
    isOffset: boolean;
    isSlot: boolean;
    src: SourceLocation;
    valueSize: number;
    suffix?: "slot" | "offset" | "length";
  }[];
  flags?: "memory-safe"[];
  nodeType: "InlineAssembly";
}
export interface YulBlock {
  src: SourceLocation;
  statements: YulStatement[];
  nodeType: "YulBlock";
  nativeSrc?: SourceLocation;
}
export interface YulAssignment {
  src: SourceLocation;
  value: YulExpression;
  variableNames: YulIdentifier[];
  nodeType: "YulAssignment";
  nativeSrc?: SourceLocation;
}
export interface YulFunctionCall {
  src: SourceLocation;
  arguments: YulExpression[];
  functionName: YulIdentifier;
  nodeType: "YulFunctionCall";
  nativeSrc?: SourceLocation;
}
export interface YulIdentifier {
  src: SourceLocation;
  name: string;
  nodeType: "YulIdentifier";
  nativeSrc?: SourceLocation;
}
export interface YulLiteralValue {
  src: SourceLocation;
  value: string;
  kind: "number" | "string" | "bool";
  type: string;
  nodeType: "YulLiteral";
  nativeSrc?: SourceLocation;
}
export interface YulLiteralHexValue {
  src: SourceLocation;
  hexValue: string;
  kind: "number" | "string" | "bool";
  type: string;
  value?: string;
  nodeType: "YulLiteral";
  nativeSrc?: SourceLocation;
}
export interface YulBreak {
  src: SourceLocation;
  nodeType: "YulBreak";
  nativeSrc?: SourceLocation;
}
export interface YulContinue {
  src: SourceLocation;
  nodeType: "YulContinue";
  nativeSrc?: SourceLocation;
}
export interface YulExpressionStatement {
  src: SourceLocation;
  expression: YulExpression;
  nodeType: "YulExpressionStatement";
  nativeSrc?: SourceLocation;
}
export interface YulLeave {
  src: SourceLocation;
  nodeType: "YulLeave";
  nativeSrc?: SourceLocation;
}
export interface YulForLoop {
  src: SourceLocation;
  body: YulBlock;
  condition: YulExpression;
  post: YulBlock;
  pre: YulBlock;
  nodeType: "YulForLoop";
  nativeSrc?: SourceLocation;
}
export interface YulFunctionDefinition {
  src: SourceLocation;
  body: YulBlock;
  name: string;
  parameters?: YulTypedName[];
  returnVariables?: YulTypedName[];
  nodeType: "YulFunctionDefinition";
  nativeSrc?: SourceLocation;
}
export interface YulTypedName {
  src: SourceLocation;
  name: string;
  type: string;
  nodeType: "YulTypedName";
  nativeSrc?: SourceLocation;
}
export interface YulIf {
  src: SourceLocation;
  body: YulBlock;
  condition: YulExpression;
  nodeType: "YulIf";
  nativeSrc?: SourceLocation;
}
export interface YulSwitch {
  src: SourceLocation;
  cases: YulCase[];
  expression: YulExpression;
  nodeType: "YulSwitch";
  nativeSrc?: SourceLocation;
}
export interface YulCase {
  src: SourceLocation;
  body: YulBlock;
  value: "default" | YulLiteral;
  nodeType: "YulCase";
  nativeSrc?: SourceLocation;
}
export interface YulVariableDeclaration {
  src: SourceLocation;
  value?: YulExpression | null;
  variables: YulTypedName[];
  nodeType: "YulVariableDeclaration";
  nativeSrc?: SourceLocation;
}
export interface PlaceholderStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  nodeType: "PlaceholderStatement";
}
export interface Return {
  id: number;
  src: SourceLocation;
  documentation?: string;
  expression?: Expression | null;
  functionReturnParameters: number;
  nodeType: "Return";
}
export interface RevertStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  errorCall: FunctionCall;
  nodeType: "RevertStatement";
}
export interface TryStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  clauses: TryCatchClause[];
  externalCall: FunctionCall;
  nodeType: "TryStatement";
}
export interface TryCatchClause {
  id: number;
  src: SourceLocation;
  block: Block;
  errorName: string;
  parameters?: ParameterList | null;
  nodeType: "TryCatchClause";
}
export interface UncheckedBlock {
  id: number;
  src: SourceLocation;
  documentation?: string;
  statements: Statement[];
  nodeType: "UncheckedBlock";
}
export interface WhileStatement {
  id: number;
  src: SourceLocation;
  documentation?: string;
  body: Block | Statement;
  condition: Expression;
  nodeType: "WhileStatement";
}
export interface ModifierInvocation {
  id: number;
  src: SourceLocation;
  arguments?: Expression[] | null;
  kind?: "modifierInvocation" | "baseConstructorSpecifier";
  modifierName: Identifier | IdentifierPath;
  nodeType: "ModifierInvocation";
}
export interface ModifierDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  baseModifiers?: number[] | null;
  body?: Block | null;
  documentation?: StructuredDocumentation | null;
  overrides?: OverrideSpecifier | null;
  parameters: ParameterList;
  virtual: boolean;
  visibility: Visibility;
  nodeType: "ModifierDefinition";
}
export interface StructDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  canonicalName: string;
  members: VariableDeclaration[];
  scope: number;
  visibility: Visibility;
  documentation?: StructuredDocumentation | null;
  nodeType: "StructDefinition";
}
export interface UserDefinedValueTypeDefinition {
  id: number;
  src: SourceLocation;
  name: string;
  nameLocation?: string;
  canonicalName?: string;
  underlyingType: TypeName;
  nodeType: "UserDefinedValueTypeDefinition";
}
export interface UsingForDirective {
  id: number;
  src: SourceLocation;
  functionList?: (
    | {
        function: IdentifierPath;
      }
    | {
        operator: "&" | "|" | "^" | "~" | "+" | "-" | "*" | "/" | "%" | "==" | "!=" | "<" | "<=" | ">" | ">=";
        definition: IdentifierPath;
      }
  )[];
  global?: boolean;
  libraryName?: UserDefinedTypeName | IdentifierPath;
  typeName?: TypeName | null;
  nodeType: "UsingForDirective";
}
export interface ImportDirective {
  id: number;
  src: SourceLocation;
  absolutePath: string;
  file: string;
  nameLocation?: string;
  scope: number;
  sourceUnit: number;
  symbolAliases: {
    foreign: Identifier;
    local?: string | null;
    nameLocation?: string;
  }[];
  unitAlias: string;
  nodeType: "ImportDirective";
}
export interface PragmaDirective {
  id: number;
  src: SourceLocation;
  literals: string[];
  nodeType: "PragmaDirective";
}
