import type {SourceUnit, ArrayTypeName, Assignment, BinaryOperation, Block, Break, Conditional, Continue, ContractDefinition, DoWhileStatement, ElementaryTypeName, ElementaryTypeNameExpression, EmitStatement, EnumDefinition, EnumValue, ErrorDefinition, EventDefinition, ExpressionStatement, ForStatement, FunctionCall, FunctionCallOptions, FunctionDefinition, FunctionTypeName, Identifier, IdentifierPath, IfStatement, ImportDirective, IndexAccess, IndexRangeAccess, InheritanceSpecifier, InlineAssembly, Literal, Mapping, MemberAccess, ModifierDefinition, ModifierInvocation, NewExpression, OverrideSpecifier, ParameterList, PlaceholderStatement, PragmaDirective, Return, RevertStatement, StructDefinition, StructuredDocumentation, TryCatchClause, TryStatement, TupleExpression, UnaryOperation, UncheckedBlock, UserDefinedTypeName, UserDefinedValueTypeDefinition, UsingForDirective, VariableDeclaration, VariableDeclarationStatement, WhileStatement} from './types';
export type Node = SourceUnit | ArrayTypeName | Assignment | BinaryOperation | Block | Break | Conditional | Continue | ContractDefinition | DoWhileStatement | ElementaryTypeName | ElementaryTypeNameExpression | EmitStatement | EnumDefinition | EnumValue | ErrorDefinition | EventDefinition | ExpressionStatement | ForStatement | FunctionCall | FunctionCallOptions | FunctionDefinition | FunctionTypeName | Identifier | IdentifierPath | IfStatement | ImportDirective | IndexAccess | IndexRangeAccess | InheritanceSpecifier | InlineAssembly | Literal | Mapping | MemberAccess | ModifierDefinition | ModifierInvocation | NewExpression | OverrideSpecifier | ParameterList | PlaceholderStatement | PragmaDirective | Return | RevertStatement | StructDefinition | StructuredDocumentation | TryCatchClause | TryStatement | TupleExpression | UnaryOperation | UncheckedBlock | UserDefinedTypeName | UserDefinedValueTypeDefinition | UsingForDirective | VariableDeclaration | VariableDeclarationStatement | WhileStatement;
export type NodeTypeMap = { SourceUnit: SourceUnit, ArrayTypeName: ArrayTypeName, Assignment: Assignment, BinaryOperation: BinaryOperation, Block: Block, Break: Break, Conditional: Conditional, Continue: Continue, ContractDefinition: ContractDefinition, DoWhileStatement: DoWhileStatement, ElementaryTypeName: ElementaryTypeName, ElementaryTypeNameExpression: ElementaryTypeNameExpression, EmitStatement: EmitStatement, EnumDefinition: EnumDefinition, EnumValue: EnumValue, ErrorDefinition: ErrorDefinition, EventDefinition: EventDefinition, ExpressionStatement: ExpressionStatement, ForStatement: ForStatement, FunctionCall: FunctionCall, FunctionCallOptions: FunctionCallOptions, FunctionDefinition: FunctionDefinition, FunctionTypeName: FunctionTypeName, Identifier: Identifier, IdentifierPath: IdentifierPath, IfStatement: IfStatement, ImportDirective: ImportDirective, IndexAccess: IndexAccess, IndexRangeAccess: IndexRangeAccess, InheritanceSpecifier: InheritanceSpecifier, InlineAssembly: InlineAssembly, Literal: Literal, Mapping: Mapping, MemberAccess: MemberAccess, ModifierDefinition: ModifierDefinition, ModifierInvocation: ModifierInvocation, NewExpression: NewExpression, OverrideSpecifier: OverrideSpecifier, ParameterList: ParameterList, PlaceholderStatement: PlaceholderStatement, PragmaDirective: PragmaDirective, Return: Return, RevertStatement: RevertStatement, StructDefinition: StructDefinition, StructuredDocumentation: StructuredDocumentation, TryCatchClause: TryCatchClause, TryStatement: TryStatement, TupleExpression: TupleExpression, UnaryOperation: UnaryOperation, UncheckedBlock: UncheckedBlock, UserDefinedTypeName: UserDefinedTypeName, UserDefinedValueTypeDefinition: UserDefinedValueTypeDefinition, UsingForDirective: UsingForDirective, VariableDeclaration: VariableDeclaration, VariableDeclarationStatement: VariableDeclarationStatement, WhileStatement: WhileStatement };
export type NodeType = keyof NodeTypeMap;
import type {YulLiteral, YulAssignment, YulBlock, YulBreak, YulCase, YulContinue, YulExpressionStatement, YulFunctionCall, YulForLoop, YulFunctionDefinition, YulIdentifier, YulIf, YulLeave, YulSwitch, YulTypedName, YulVariableDeclaration} from './types';
export type YulNode = YulLiteral | YulAssignment | YulBlock | YulBreak | YulCase | YulContinue | YulExpressionStatement | YulFunctionCall | YulForLoop | YulFunctionDefinition | YulIdentifier | YulIf | YulLeave | YulSwitch | YulTypedName | YulVariableDeclaration;
export type YulNodeTypeMap = { YulLiteral: YulLiteral, YulAssignment: YulAssignment, YulBlock: YulBlock, YulBreak: YulBreak, YulCase: YulCase, YulContinue: YulContinue, YulExpressionStatement: YulExpressionStatement, YulFunctionCall: YulFunctionCall, YulForLoop: YulForLoop, YulFunctionDefinition: YulFunctionDefinition, YulIdentifier: YulIdentifier, YulIf: YulIf, YulLeave: YulLeave, YulSwitch: YulSwitch, YulTypedName: YulTypedName, YulVariableDeclaration: YulVariableDeclaration };
export type YulNodeType = keyof YulNodeTypeMap;