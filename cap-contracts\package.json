{"name": "cap-contracts", "version": "1.0.0", "description": "Cap Labs Core Contracts", "main": "index.js", "author": "Cap Labs Inc.", "license": "MIT", "private": true, "engines": {"node": "^20.18.0"}, "scripts": {"postinstall": "lefthook install && forge remappings > remappings.txt", "compile": "forge build", "build": "forge build --skip Test", "check-access": "forge script script/manage/CheckAccess.s.sol:CheckAccess --via-ir --rpc-url ethereum", "check-access:save": "yarn --silent check-access > access.txt", "test": "forge test", "test:unit": "forge test --no-match-path 'test/**/*.invariants.t.sol'", "test:invariants": "forge test --match-path 'test/**/*.invariants.t.sol'", "test:invariants:deep": "FOUNDRY_PROFILE=deep forge test --match-path 'test/**/*.invariants.t.sol'", "test:gas": "forge test --match-path 'test/**/*.gas.t.sol'", "test:slither": "FOUNDRY_PROFILE=slither slither . --config slither.config.json", "test:gambit:gen": "./test/mutation/gambit.sh generate", "test:gambit:run": "./test/mutation/gambit.sh test 8", "test:sumo": "sumo test", "test:build": "forge build ./contracts ./test ./script --via-ir", "gas:flamegraph": "forge test --flamegraph --match-path 'test/**/*.gas.t.sol'", "coverage:forge": "forge coverage --report summary --ir-minimum", "coverage:forge:report": "forge coverage --report lcov --ir-minimum && lcov --remove lcov.info \"*test/*\" \"lendingPool/libraries/math\" \"*/contracts/deploy/*\" \"*script/*\" \"*node_modules/*\" --output-file lcov.info --rc lcov_branch_coverage=1 --ignore-errors inconsistent --ignore-errors unused && genhtml lcov.info --branch-coverage --output-dir coverage --ignore-errors inconsistent --ignore-errors unused"}, "devDependencies": {"@openzeppelin/contracts": "npm:@openzeppelin/contracts", "@openzeppelin/contracts-upgradeable": "npm:@openzeppelin/contracts-upgradeable", "@symbioticfi/burners": "^1.0.0-devnet.2", "@symbioticfi/core": "^1.0.0-devnet.10", "@symbioticfi/hooks": "^1.0.0-devnet.1", "@symbioticfi/rewards": "^1.0.0-devnet.6", "eigenlayer-contracts": "^1.0.4", "lefthook": "^1.10.10"}, "resolutions": {"@openzeppelin/contracts": "^5.2.0"}, "dependencies": {"@morenabarboni/sumo": "^2.5.4", "@openzeppelin/foundry-upgrades": "^0.3.7", "eigenlayer": "^2.1.5"}}