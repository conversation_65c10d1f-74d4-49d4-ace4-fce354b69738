{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol": {"lastModificationDate": 1730736797024, "contentHash": "8a3641960f26688f0dccae636ce6d1b0", "sourceName": "src/contracts/core/AVSDirectory.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "../permissions/Pausable.sol", "../libraries/EIP1271SignatureUtils.sol", "./AVSDirectoryStorage.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["AVSDirectory"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/permissions/Pausable.sol": {"lastModificationDate": 1730704250573, "contentHash": "9673586545a7a38943b5ca981060f9ae", "sourceName": "src/contracts/permissions/Pausable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IPausable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["Pausable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol": {"lastModificationDate": 1730749075914, "contentHash": "554e9d8977899862c7fe89e9740d81b3", "sourceName": "src/contracts/libraries/EIP1271SignatureUtils.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/interfaces/IERC1271.sol", "openzeppelin/contracts/utils/Address.sol", "openzeppelin/contracts/utils/cryptography/ECDSA.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["EIP1271SignatureUtils"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol": {"lastModificationDate": 1730704250568, "contentHash": "8c1c4345d820d68403df95fee7c1d591", "sourceName": "src/contracts/core/AVSDirectoryStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IAVSDirectory.sol", "../interfaces/IDelegationManager.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["AVSDirectoryStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol": {"lastModificationDate": 1730750045864, "contentHash": "c9cde6037fc8b1fe0ef04b79149ba733", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ReentrancyGuardUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"lastModificationDate": 1730750045743, "contentHash": "403ce8273abde646bff81558ddf512ad", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../utils/ContextUpgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["OwnableUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"lastModificationDate": 1730750045860, "contentHash": "b98e2f3a856e6e7f2106fb919bacab9e", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../../utils/AddressUpgradeable.sol"], "versionPragmas": ["^0.8.2"], "artifacts": ["Initializable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol": {"lastModificationDate": 1730704250570, "contentHash": "81446a04df973d0aabba7c2178370a6c", "sourceName": "src/contracts/interfaces/IPausable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IPauserRegistry.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IPausable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol": {"lastModificationDate": 1730704250570, "contentHash": "f8660f151eaa5ef574c8fe237bab126f", "sourceName": "src/contracts/interfaces/IPauserRegistry.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IPauserRegistry"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/utils/Address.sol": {"lastModificationDate": 1730749074569, "contentHash": "c476b3895a94798b88a4bb97399e6dfe", "sourceName": "openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["Address"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/interfaces/IERC1271.sol": {"lastModificationDate": 1730749074598, "contentHash": "8fe867b95c856b204f954a1910e28a1e", "sourceName": "openzeppelin/contracts/interfaces/IERC1271.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1271"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1730749074579, "contentHash": "1dfb7cf7c7e2edae73403d50a59cc967", "sourceName": "openzeppelin/contracts/utils/cryptography/ECDSA.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../Strings.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ECDSA"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1730749074611, "contentHash": "cf46906c4035f51639a22265066a9e78", "sourceName": "openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Strings"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol": {"lastModificationDate": 1730704250569, "contentHash": "d5b129d0f65915d6f261a0a4fa603446", "sourceName": "src/contracts/interfaces/IAVSDirectory.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./ISignatureUtils.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IAVSDirectory"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol": {"lastModificationDate": 1730704250570, "contentHash": "1b9a7700899f112ad9c78b9db96f8980", "sourceName": "src/contracts/interfaces/IDelegationManager.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./IStrategy.sol", "./ISignatureUtils.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IDelegationManager"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol": {"lastModificationDate": 1730704250571, "contentHash": "89380251e8b83383f70e8ed6365c30df", "sourceName": "src/contracts/interfaces/ISignatureUtils.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["ISignatureUtils"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol": {"lastModificationDate": 1730749071841, "contentHash": "a896d58757d151a922d62d701041a0f5", "sourceName": "src/contracts/interfaces/IStrategy.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IStrategy"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1730749074600, "contentHash": "ad7c2d0af148c8f9f097d65deeb4da6b", "sourceName": "openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol": {"lastModificationDate": 1730750045892, "contentHash": "d42e87f4fba2b03ab4d3c14cb53d0c51", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["AddressUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"lastModificationDate": 1730750045893, "contentHash": "6200b84950eb05b4a92a39fd1d6e0f9b", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ContextUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/strategies/StrategyFactory.sol": {"lastModificationDate": 1730749076534, "contentHash": "64f61a2057a4038bd1ba294fbfbff655", "sourceName": "src/contracts/strategies/StrategyFactory.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "./StrategyFactoryStorage.sol", "./StrategyBase.sol", "../permissions/Pausable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["StrategyFactory"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/strategies/StrategyFactoryStorage.sol": {"lastModificationDate": 1730704250574, "contentHash": "46ec5b2ef9cf0972e79c2535385f1490", "sourceName": "src/contracts/strategies/StrategyFactoryStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IStrategyFactory.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["StrategyFactoryStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol": {"lastModificationDate": 1730749076325, "contentHash": "73bb406aa91f3b3790ccd1b7c1d68bee", "sourceName": "src/contracts/strategies/StrategyBase.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IStrategyManager.sol", "../permissions/Pausable.sol", "openzeppelin/contracts/token/ERC20/IERC20.sol", "openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["StrategyBase"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/proxy/beacon/BeaconProxy.sol": {"lastModificationDate": 1730749074571, "contentHash": "dc9dcb6e542154d9cfbfaece646c1092", "sourceName": "openzeppelin/contracts/proxy/beacon/BeaconProxy.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./IBeacon.sol", "../Proxy.sol", "../ERC1967/ERC1967Upgrade.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["BeaconProxy"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IStrategyFactory.sol": {"lastModificationDate": 1730749071885, "contentHash": "8684b364e68f84f80d7f0a9f0dd04082", "sourceName": "src/contracts/interfaces/IStrategyFactory.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/proxy/beacon/IBeacon.sol", "openzeppelin/contracts/token/ERC20/IERC20.sol", "./IStrategy.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["IStrategyFactory"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/proxy/beacon/IBeacon.sol": {"lastModificationDate": 1730749074596, "contentHash": "b6bd23bf19e90b771337037706470933", "sourceName": "openzeppelin/contracts/proxy/beacon/IBeacon.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IBeacon"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol": {"lastModificationDate": 1730704250571, "contentHash": "a9f28fdbd73f6619fdb1e8806d34da24", "sourceName": "src/contracts/interfaces/IStrategyManager.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./IStrategy.sol", "./ISlasher.sol", "./IDelegationManager.sol", "./IEigenPodManager.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IStrategyManager"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1730749074600, "contentHash": "909ab67fc5c25033fe6cd364f8c056f9", "sourceName": "openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Metadata"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"lastModificationDate": 1730749074610, "contentHash": "3a843b05b85a270e9455e3d2e804e633", "sourceName": "openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../IERC20.sol", "../extensions/draft-IERC20Permit.sol", "../../../utils/Address.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["SafeERC20"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol": {"lastModificationDate": 1730704250571, "contentHash": "2420fa4011cf315bdb56b640fe0ba307", "sourceName": "src/contracts/interfaces/ISlasher.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./IStrategyManager.sol", "./IDelegationManager.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["ISlasher"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol": {"lastModificationDate": 1730749071885, "contentHash": "9a815b6e6c02b834c5ed165b76c52630", "sourceName": "src/contracts/interfaces/IEigenPodManager.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/proxy/beacon/IBeacon.sol", "./IETHPOSDeposit.sol", "./IStrategyManager.sol", "./IEigenPod.sol", "./IPausable.sol", "./ISlasher.sol", "./IStrategy.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IEigenPodManager"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol": {"lastModificationDate": 1730704250570, "contentHash": "4f6da7ff2685909ce76b5aff81db1b99", "sourceName": "src/contracts/interfaces/IETHPOSDeposit.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IETHPOSDeposit"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol": {"lastModificationDate": 1730749071885, "contentHash": "721f4af14602ad3a97de2335e4153aa9", "sourceName": "src/contracts/interfaces/IEigenPod.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../libraries/BeaconChainProofs.sol", "./IEigenPodManager.sol", "openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IEigenPod"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol": {"lastModificationDate": 1730704250572, "contentHash": "fcf5ecadfeace6c39f7ca3d2a1e9c1d1", "sourceName": "src/contracts/libraries/BeaconChainProofs.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./Merkle.sol", "../libraries/Endian.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["BeaconChainProofs"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/libraries/Endian.sol": {"lastModificationDate": 1730704250572, "contentHash": "6423d2c9344d84c22f4e347a973ba7b5", "sourceName": "src/contracts/libraries/Endian.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["<PERSON><PERSON>"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/libraries/Merkle.sol": {"lastModificationDate": 1730704250572, "contentHash": "f81f144f4f61239fdb4867215cc95155", "sourceName": "src/contracts/libraries/Merkle.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["<PERSON><PERSON><PERSON>"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/token/ERC20/extensions/draft-IERC20Permit.sol": {"lastModificationDate": 1730749074578, "contentHash": "fb77f144244b9ab12533aa6ce85ef8c5", "sourceName": "openzeppelin/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Permit"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/proxy/Proxy.sol": {"lastModificationDate": 1730749074609, "contentHash": "40b3d81a836d50ff47e03893dcaaf204", "sourceName": "openzeppelin/contracts/proxy/Proxy.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Proxy"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/proxy/ERC1967/ERC1967Upgrade.sol": {"lastModificationDate": 1730749074584, "contentHash": "6baa887a798e95b14f34e093f117e9b2", "sourceName": "openzeppelin/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../beacon/IBeacon.sol", "../../interfaces/draft-IERC1822.sol", "../../utils/Address.sol", "../../utils/StorageSlot.sol"], "versionPragmas": ["^0.8.2"], "artifacts": ["ERC1967Upgrade"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/interfaces/draft-IERC1822.sol": {"lastModificationDate": 1730749074578, "contentHash": "2858d98e74e67987ec81b39605230b74", "sourceName": "openzeppelin/contracts/interfaces/draft-IERC1822.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1822Proxiable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1730749074611, "contentHash": "f993f8f50186952a59ee5e3a30b68222", "sourceName": "openzeppelin/contracts/utils/StorageSlot.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["StorageSlot"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/strategies/StrategyBaseTVLLimits.sol": {"lastModificationDate": 1730704250574, "contentHash": "c920d1376495e0eccd813696434a5f11", "sourceName": "src/contracts/strategies/StrategyBaseTVLLimits.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./StrategyBase.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["StrategyBaseTVLLimits"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol": {"lastModificationDate": 1730749075889, "contentHash": "f2c1d7b2d2e3796d32e44e073ea401cb", "sourceName": "src/contracts/pods/EigenPodManager.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/utils/Create2.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "../permissions/Pausable.sol", "./EigenPodPausingConstants.sol", "./EigenPodManagerStorage.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["EigenPodManager"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol": {"lastModificationDate": 1730704250573, "contentHash": "2f49a00ca803d0e7e17816dde4843f42", "sourceName": "src/contracts/pods/EigenPodPausingConstants.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.12"], "artifacts": ["EigenPodPausingConstants"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol": {"lastModificationDate": 1730749075776, "contentHash": "8ec10a0aaa51ca59e946151fec3c62e9", "sourceName": "src/contracts/pods/EigenPodManagerStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/proxy/beacon/IBeacon.sol", "../interfaces/IStrategy.sol", "../interfaces/IEigenPodManager.sol", "../interfaces/IStrategyManager.sol", "../interfaces/IDelegationManager.sol", "../interfaces/IETHPOSDeposit.sol", "../interfaces/IEigenPod.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["EigenPodManagerStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/node_modules/openzeppelin/contracts/utils/Create2.sol": {"lastModificationDate": 1730749074574, "contentHash": "8932e2855e9aac6e79af79e499863b10", "sourceName": "openzeppelin/contracts/utils/Create2.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Create2"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/pods/EigenPod.sol": {"lastModificationDate": 1730749075698, "contentHash": "b1eee23914c79c13e2d09d4e855a7a65", "sourceName": "src/contracts/pods/EigenPod.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "../libraries/BeaconChainProofs.sol", "../libraries/BytesLib.sol", "../interfaces/IETHPOSDeposit.sol", "../interfaces/IEigenPodManager.sol", "../interfaces/IPausable.sol", "./EigenPodPausingConstants.sol", "./EigenPodStorage.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["EigenPod"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol": {"lastModificationDate": 1730704250572, "contentHash": "4b108962dab16029195dde673c795b67", "sourceName": "src/contracts/libraries/BytesLib.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": [">=0.8.0 <0.9.0"], "artifacts": ["BytesLib"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol": {"lastModificationDate": 1730704250573, "contentHash": "4c5358be3bd3a1eb3ec165cead077e62", "sourceName": "src/contracts/pods/EigenPodStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IEigenPod.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["EigenPodStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/strategies/EigenStrategy.sol": {"lastModificationDate": 1730749076534, "contentHash": "5ba395937dac0659e6413535dd7dfc0c", "sourceName": "src/contracts/strategies/EigenStrategy.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "../interfaces/IStrategyManager.sol", "../strategies/StrategyBase.sol", "../interfaces/IEigen.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["EigenStrategy"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IEigen.sol": {"lastModificationDate": 1730749071892, "contentHash": "ce26e67020428f68342c8ef996a033ed", "sourceName": "src/contracts/interfaces/IEigen.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IEigen"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol": {"lastModificationDate": 1730704250573, "contentHash": "1beba8d31e76b28a6c56f78007e41e2f", "sourceName": "src/contracts/permissions/PauserRegistry.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IPauserRegistry.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["PauserRegistry"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol": {"lastModificationDate": 1730704250569, "contentHash": "72c43df54fb523fe8084eed2288b8482", "sourceName": "src/contracts/core/StrategyManagerStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IStrategyManager.sol", "../interfaces/IStrategy.sol", "../interfaces/IEigenPodManager.sol", "../interfaces/IDelegationManager.sol", "../interfaces/ISlasher.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["StrategyManagerStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/StrategyManager.sol": {"lastModificationDate": 1730749075991, "contentHash": "bbf2f8fec6f5859ef0fe1f3f3a8214b6", "sourceName": "src/contracts/core/StrategyManager.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "../interfaces/IEigenPodManager.sol", "../permissions/Pausable.sol", "./StrategyManagerStorage.sol", "../libraries/EIP1271SignatureUtils.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["StrategyManager"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/DelegationManager.sol": {"lastModificationDate": 1730736797182, "contentHash": "e6d21a752e355642fa744d02bef2dae8", "sourceName": "src/contracts/core/DelegationManager.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "../permissions/Pausable.sol", "../libraries/EIP1271SignatureUtils.sol", "./DelegationManagerStorage.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["DelegationManager"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol": {"lastModificationDate": 1730704250569, "contentHash": "a698eefc0931659fd047622da11f6c66", "sourceName": "src/contracts/core/DelegationManagerStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IStrategyManager.sol", "../interfaces/IDelegationManager.sol", "../interfaces/ISlasher.sol", "../interfaces/IEigenPodManager.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["DelegationManagerStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/Slasher.sol": {"lastModificationDate": 1730736797091, "contentHash": "5a001fe32b1e4a083dc21548d9f7450d", "sourceName": "src/contracts/core/Slasher.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/ISlasher.sol", "../interfaces/IDelegationManager.sol", "../interfaces/IStrategyManager.sol", "../permissions/Pausable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["<PERSON>lasher"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IDelegationFaucet.sol": {"lastModificationDate": 1730749071834, "contentHash": "8fce503d2de667a27fe2f63673268bef", "sourceName": "src/contracts/interfaces/IDelegationFaucet.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["src/contracts/interfaces/IDelegationManager.sol", "openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IDelegationFaucet"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol": {"lastModificationDate": 1730704250569, "contentHash": "4f83e3142214e38c94f17ab27ae4397c", "sourceName": "src/contracts/core/RewardsCoordinatorStorage.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../interfaces/IStrategyManager.sol", "../interfaces/IDelegationManager.sol", "../interfaces/IRewardsCoordinator.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["RewardsCoordinatorStorage"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol": {"lastModificationDate": 1730749071834, "contentHash": "386001e540e822ea04098acae9e9a4cb", "sourceName": "src/contracts/interfaces/IRewardsCoordinator.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/IERC20.sol", "./IStrategy.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["IRewardsCoordinator"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol": {"lastModificationDate": 1730749076143, "contentHash": "53ae8f138ba5a39b23805a520b33cae5", "sourceName": "src/contracts/core/RewardsCoordinator.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "../libraries/Merkle.sol", "../permissions/Pausable.sol", "./RewardsCoordinatorStorage.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["RewardsCoordinator"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/IBackingEigen.sol": {"lastModificationDate": 1730749071807, "contentHash": "4799f1bcebc6cd9860469ffb8aa35eda", "sourceName": "src/contracts/interfaces/IBackingEigen.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["IBackingEigen"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/utils/UpgradeableSignatureCheckingUtils.sol": {"lastModificationDate": 1730736796913, "contentHash": "aa63b0304fb21050ecb87301fc9c4867", "sourceName": "src/contracts/utils/UpgradeableSignatureCheckingUtils.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["UpgradeableSignatureCheckingUtils"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/interfaces/ISocketUpdater.sol": {"lastModificationDate": 1730704250571, "contentHash": "7e02d2fad2f7305813ee95a11a2a6d01", "sourceName": "src/contracts/interfaces/ISocketUpdater.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.12"], "artifacts": ["ISocketUpdater"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/libraries/StructuredLinkedList.sol": {"lastModificationDate": 1730704250572, "contentHash": "739bae0ef39f55db5f719b2a25e919f4", "sourceName": "src/contracts/libraries/StructuredLinkedList.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.12"], "artifacts": ["StructuredLinkedList"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/token/BackingEigen.sol": {"lastModificationDate": 1730749533844, "contentHash": "6961366e0cd937fff4e93d36aa031979", "sourceName": "src/contracts/token/BackingEigen.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["BackingEigen"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/src/contracts/token/Eigen.sol": {"lastModificationDate": 1730749507895, "contentHash": "aa29959f31d72cd6cb9516a288cccc98", "sourceName": "src/contracts/token/Eigen.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["openzeppelin/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol"], "versionPragmas": ["^0.8.12"], "artifacts": ["Eigen"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/draft-EIP712Upgradeable.sol": {"lastModificationDate": 1730750045898, "contentHash": "58bfd44eb122c6d88351e8eda0590068", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/draft-EIP712Upgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./ECDSAUpgradeable.sol", "../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["EIP712Upgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol": {"lastModificationDate": 1730750045897, "contentHash": "c6eab8461f78befdc423f1d520ea5a97", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../StringsUpgradeable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ECDSAUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol": {"lastModificationDate": 1730750045895, "contentHash": "b9d00086379b2524eb287c850a1b2e54", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["StringsUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/draft-ERC20PermitUpgradeable.sol": {"lastModificationDate": 1730750045879, "contentHash": "0109bc74226813045cdebae4027b777d", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/draft-ERC20PermitUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./draft-IERC20PermitUpgradeable.sol", "../ERC20Upgradeable.sol", "../../../utils/cryptography/draft-EIP712Upgradeable.sol", "../../../utils/cryptography/ECDSAUpgradeable.sol", "../../../utils/CountersUpgradeable.sol", "../../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC20PermitUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol": {"lastModificationDate": 1730750045873, "contentHash": "f6a254bc3e3c9c456a7a630fc4cb8ca4", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./IERC20Upgradeable.sol", "./extensions/IERC20MetadataUpgradeable.sol", "../../utils/ContextUpgradeable.sol", "../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC20Upgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/CountersUpgradeable.sol": {"lastModificationDate": 1730750045893, "contentHash": "2e908c762a799baea365e68a50500e2c", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/CountersUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["CountersUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/draft-IERC20PermitUpgradeable.sol": {"lastModificationDate": 1730750045880, "contentHash": "afab576c4d1f55fbf293a0daf3826e31", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/draft-IERC20PermitUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20PermitUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/IERC20Upgradeable.sol": {"lastModificationDate": 1730750045873, "contentHash": "a9ed3c7bc7d9ebb5e34f20829274f088", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/IERC20Upgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Upgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol": {"lastModificationDate": 1730750045879, "contentHash": "9efcd5467a7f0bf533910ee8a267adb2", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/IERC20MetadataUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["../IERC20Upgradeable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20MetadataUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol": {"lastModificationDate": 1730750045878, "contentHash": "9c9a433acb6324ceaaa657c804f1a03c", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/extensions/ERC20VotesUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": ["./draft-ERC20PermitUpgradeable.sol", "../../../utils/math/MathUpgradeable.sol", "../../../governance/utils/IVotesUpgradeable.sol", "../../../utils/math/SafeCastUpgradeable.sol", "../../../utils/cryptography/ECDSAUpgradeable.sol", "../../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC20VotesUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol": {"lastModificationDate": 1730750045902, "contentHash": "bd2782c8789a3d3084d67c351a55ab92", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["MathUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/governance/utils/IVotesUpgradeable.sol": {"lastModificationDate": 1730750045771, "contentHash": "432a27bbcb26badb3d40e9fb214d09f9", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/governance/utils/IVotesUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IVotesUpgradeable"]}, "/Users/<USER>/Desktop/code/eigenlayer-contracts/lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol": {"lastModificationDate": 1730750045903, "contentHash": "00d9c0b8534f648176c53b50a914c19a", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "solcConfig": {"version": "0.8.12", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["SafeCastUpgradeable"]}}}