{"_format": "", "paths": {"artifacts": "out", "build_infos": "out/build-info", "sources": "src", "tests": "test", "scripts": "script", "libraries": ["lib"]}, "files": {"lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol": {"lastModificationDate": 1730732614106, "contentHash": "39fd1ac607566a7bd02653d4f3790952", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"AVSDirectory": {"0.8.12": {"path": "AVSDirectory.sol/AVSDirectory.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol": {"lastModificationDate": 1730695187336, "contentHash": "8c1c4345d820d68403df95fee7c1d591", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"AVSDirectoryStorage": {"0.8.12": {"path": "AVSDirectoryStorage.sol/AVSDirectoryStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol": {"lastModificationDate": 1730732614396, "contentHash": "7eaa0b50f6b4a45ce75377713c387655", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationManager": {"0.8.12": {"path": "DelegationManager.sol/DelegationManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol": {"lastModificationDate": 1730695187338, "contentHash": "a698eefc0931659fd047622da11f6c66", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationManagerStorage": {"0.8.12": {"path": "DelegationManagerStorage.sol/DelegationManagerStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol": {"lastModificationDate": 1730732614232, "contentHash": "dbc0656db6e49e2e9dfb7866d43b1be9", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RewardsCoordinator": {"0.8.12": {"path": "RewardsCoordinator.sol/RewardsCoordinator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol": {"lastModificationDate": 1730695187339, "contentHash": "c35a7815f1ab9effb5fc668a8c74b51b", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RewardsCoordinatorStorage": {"0.8.12": {"path": "RewardsCoordinatorStorage.sol/RewardsCoordinatorStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol": {"lastModificationDate": 1730732614183, "contentHash": "4af0fcef7a9ad9b52ae12f00384b95b7", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Slasher": {"0.8.12": {"path": "Slasher.sol/Slasher.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol": {"lastModificationDate": 1730732614269, "contentHash": "854d913b7aa0ddeeab310c92d6f0ebd5", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManager": {"0.8.12": {"path": "StrategyManager.sol/StrategyManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol": {"lastModificationDate": 1730695187340, "contentHash": "72c43df54fb523fe8084eed2288b8482", "sourceName": "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManagerStorage": {"0.8.12": {"path": "StrategyManagerStorage.sol/StrategyManagerStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol": {"lastModificationDate": 1730695187341, "contentHash": "402ddc8bbb0d82009681130772f34784", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IAVSDirectory": {"0.8.12": {"path": "IAVSDirectory.sol/IAVSDirectory.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol": {"lastModificationDate": 1730695187343, "contentHash": "45e1dc764a1d2c451e4015afe0155412", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IDelegationManager": {"0.8.12": {"path": "IDelegationManager.sol/IDelegationManager.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol": {"lastModificationDate": 1730695187344, "contentHash": "4f6da7ff2685909ce76b5aff81db1b99", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"IETHPOSDeposit": {"0.8.12": {"path": "IETHPOSDeposit.sol/IETHPOSDeposit.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol": {"lastModificationDate": 1730695187345, "contentHash": "5002ab0787a9bd1bc357e1fc133a1303", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IEigenPod": {"0.8.12": {"path": "IEigenPod.sol/IEigenPod.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol": {"lastModificationDate": 1730695187346, "contentHash": "0fd6efd9b55ae9bb8dfde21b2d2018a1", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IEigenPodManager": {"0.8.12": {"path": "IEigenPodManager.sol/IEigenPodManager.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol": {"lastModificationDate": 1730695187346, "contentHash": "81446a04df973d0aabba7c2178370a6c", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IPausable": {"0.8.12": {"path": "IPausable.sol/IPausable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol": {"lastModificationDate": 1730695187346, "contentHash": "f8660f151eaa5ef574c8fe237bab126f", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"IPauserRegistry": {"0.8.12": {"path": "IPauserRegistry.sol/IPauserRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol": {"lastModificationDate": 1730695187347, "contentHash": "dd4e25fedbe40d25cff34d36276eec19", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IRewardsCoordinator": {"0.8.12": {"path": "IRewardsCoordinator.sol/IRewardsCoordinator.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol": {"lastModificationDate": 1730695187347, "contentHash": "89380251e8b83383f70e8ed6365c30df", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"ISignatureUtils": {"0.8.12": {"path": "ISignatureUtils.sol/ISignatureUtils.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol": {"lastModificationDate": 1730695187347, "contentHash": "2420fa4011cf315bdb56b640fe0ba307", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"ISlasher": {"0.8.12": {"path": "ISlasher.sol/ISlasher.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol": {"lastModificationDate": 1730695187348, "contentHash": "86a82749739c27d7baed6c44ef309c21", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IStrategy": {"0.8.12": {"path": "IStrategy.sol/IStrategy.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol": {"lastModificationDate": 1730695187349, "contentHash": "f7621538dfd8f5e1cdec8b438c053cee", "sourceName": "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IStrategyManager": {"0.8.12": {"path": "IStrategyManager.sol/IStrategyManager.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol": {"lastModificationDate": 1730695187350, "contentHash": "fcf5ecadfeace6c39f7ca3d2a1e9c1d1", "sourceName": "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BeaconChainProofs": {"0.8.12": {"path": "BeaconChainProofs.sol/BeaconChainProofs.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol": {"lastModificationDate": 1730695187351, "contentHash": "4b108962dab16029195dde673c795b67", "sourceName": "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.8.0, <0.9.0", "artifacts": {"BytesLib": {"0.8.12": {"path": "BytesLib.sol/BytesLib.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol": {"lastModificationDate": 1730695187351, "contentHash": "3517cb096b4a96beb140462b69e61027", "sourceName": "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EIP1271SignatureUtils": {"0.8.12": {"path": "EIP1271SignatureUtils.sol/EIP1271SignatureUtils.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol": {"lastModificationDate": 1730695187351, "contentHash": "6423d2c9344d84c22f4e347a973ba7b5", "sourceName": "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Endian": {"0.8.12": {"path": "Endian.sol/Endian.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol": {"lastModificationDate": 1730695187352, "contentHash": "f81f144f4f61239fdb4867215cc95155", "sourceName": "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Merkle": {"0.8.12": {"path": "Merkle.sol/Merkle.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol": {"lastModificationDate": 1730695187354, "contentHash": "9673586545a7a38943b5ca981060f9ae", "sourceName": "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Pausable": {"0.8.12": {"path": "Pausable.sol/Pausable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol": {"lastModificationDate": 1730695187354, "contentHash": "1beba8d31e76b28a6c56f78007e41e2f", "sourceName": "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PauserRegistry": {"0.8.12": {"path": "PauserRegistry.sol/PauserRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol": {"lastModificationDate": 1730732614467, "contentHash": "fdca28153b1fc914f07d2fb1c2c72a1e", "sourceName": "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPod": {"0.8.12": {"path": "EigenPod.sol/EigenPod.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol": {"lastModificationDate": 1730732614474, "contentHash": "220be00fcc70bf8d95271c203d9293f8", "sourceName": "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodManager": {"0.8.12": {"path": "EigenPodManager.sol/EigenPodManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol": {"lastModificationDate": 1730695187356, "contentHash": "bd070141ceda52c6221ea1b8bdebcdfb", "sourceName": "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodManagerStorage": {"0.8.12": {"path": "EigenPodManagerStorage.sol/EigenPodManagerStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol": {"lastModificationDate": 1730695187356, "contentHash": "2f49a00ca803d0e7e17816dde4843f42", "sourceName": "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodPausingConstants": {"0.8.12": {"path": "EigenPodPausingConstants.sol/EigenPodPausingConstants.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol": {"lastModificationDate": 1730695187356, "contentHash": "4c5358be3bd3a1eb3ec165cead077e62", "sourceName": "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EigenPodStorage": {"0.8.12": {"path": "EigenPodStorage.sol/EigenPodStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol": {"lastModificationDate": 1730732614481, "contentHash": "eb4198bf93aaeea1cd1a6e1bbafd94c7", "sourceName": "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyBase": {"0.8.12": {"path": "StrategyBase.sol/StrategyBase.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol": {"lastModificationDate": 1730695187386, "contentHash": "a91b7a2762ba92f64c1019b3244e4f05", "sourceName": "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ETHPOSDepositMock": {"0.8.12": {"path": "ETHDepositMock.sol/ETHPOSDepositMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol": {"lastModificationDate": 1730695187387, "contentHash": "22f633654ed79de8d778507623e62d46", "sourceName": "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.9", "artifacts": {"EigenPodManagerMock": {"0.8.12": {"path": "EigenPodManagerMock.sol/EigenPodManagerMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol": {"lastModificationDate": 1730695187387, "contentHash": "ccd00bae8ad9766e82a263f159118c9f", "sourceName": "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"EmptyContract": {"0.8.12": {"path": "EmptyContract.sol/EmptyContract.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol": {"lastModificationDate": 1730732614481, "contentHash": "c250d4d49fed48197376e4b4d416357c", "sourceName": "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StrategyManagerMock": {"0.8.12": {"path": "StrategyManagerMock.sol/StrategyManagerMock.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "lib/forge-std/src/Base.sol": {"lastModificationDate": 1730695187422, "contentHash": "ee13c050b1914464f1d3f90cde90204b", "sourceName": "lib/forge-std/src/Base.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"CommonBase": {"0.8.12": {"path": "Base.sol/CommonBase.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "ScriptBase": {"0.8.12": {"path": "Base.sol/ScriptBase.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "TestBase": {"0.8.12": {"path": "Base.sol/TestBase.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/Script.sol": {"lastModificationDate": 1730695187422, "contentHash": "ba325c778a7da8a21c2136aa32763c14", "sourceName": "lib/forge-std/src/Script.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Script": {"0.8.12": {"path": "Script.sol/Script.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdAssertions.sol": {"lastModificationDate": 1730695187423, "contentHash": "52b0ef40a9a951914f35d4135e063e48", "sourceName": "lib/forge-std/src/StdAssertions.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdAssertions": {"0.8.12": {"path": "StdAssertions.sol/StdAssertions.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdChains.sol": {"lastModificationDate": 1730695187423, "contentHash": "ad426367021e17f3fc937afa5db39a5c", "sourceName": "lib/forge-std/src/StdChains.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdChains": {"0.8.12": {"path": "StdChains.sol/StdChains.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdCheats.sol": {"lastModificationDate": 1730695187424, "contentHash": "7922ae0087a21ee3cdb97137be18c06c", "sourceName": "lib/forge-std/src/StdCheats.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console2.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdCheats": {"0.8.12": {"path": "StdCheats.sol/StdCheats.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "StdCheatsSafe": {"0.8.12": {"path": "StdCheats.sol/StdCheatsSafe.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdError.sol": {"lastModificationDate": 1730695187424, "contentHash": "64c896e1276a291776e5ea5aecb3870a", "sourceName": "lib/forge-std/src/StdError.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdError": {"0.8.12": {"path": "StdError.sol/stdError.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdInvariant.sol": {"lastModificationDate": 1730695187424, "contentHash": "0a580d6fac69e9d4b6504f747f3c0c24", "sourceName": "lib/forge-std/src/StdInvariant.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdInvariant": {"0.8.12": {"path": "StdInvariant.sol/StdInvariant.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdJson.sol": {"lastModificationDate": 1730695187425, "contentHash": "3339192c616789604138e2d8206c0702", "sourceName": "lib/forge-std/src/StdJson.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdJson": {"0.8.12": {"path": "StdJson.sol/stdJson.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdMath.sol": {"lastModificationDate": 1730695187425, "contentHash": "9da8f453eba6bb98f3d75bc6822bfb29", "sourceName": "lib/forge-std/src/StdMath.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdMath": {"0.8.12": {"path": "StdMath.sol/stdMath.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStorage.sol": {"lastModificationDate": 1730695187426, "contentHash": "5955d11c6b4a5e64839b4419e0fe71c4", "sourceName": "lib/forge-std/src/StdStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdStorage": {"0.8.12": {"path": "StdStorage.sol/stdStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "stdStorageSafe": {"0.8.12": {"path": "StdStorage.sol/stdStorageSafe.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStyle.sol": {"lastModificationDate": 1730695187426, "contentHash": "6281165a12aa639705c691fccefd855e", "sourceName": "lib/forge-std/src/StdStyle.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"StdStyle": {"0.8.12": {"path": "StdStyle.sol/StdStyle.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdToml.sol": {"lastModificationDate": 1730695187427, "contentHash": "2bb543c13f276e5db311aa3b81ed1651", "sourceName": "lib/forge-std/src/StdToml.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdToml": {"0.8.12": {"path": "StdToml.sol/stdToml.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/StdUtils.sol": {"lastModificationDate": 1730695187427, "contentHash": "2ace460f60242ec59c9310db966aee97", "sourceName": "lib/forge-std/src/StdUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdUtils": {"0.8.12": {"path": "StdUtils.sol/StdUtils.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/Test.sol": {"lastModificationDate": 1730695187428, "contentHash": "b6f15605355fc8c421fe42a90f94bf32", "sourceName": "lib/forge-std/src/Test.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Test": {"0.8.12": {"path": "Test.sol/Test.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/Vm.sol": {"lastModificationDate": 1730695187429, "contentHash": "0c248e2fef44ee2467d2e54f8d192b55", "sourceName": "lib/forge-std/src/Vm.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Vm": {"0.8.12": {"path": "Vm.sol/Vm.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "VmSafe": {"0.8.12": {"path": "Vm.sol/VmSafe.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/console.sol": {"lastModificationDate": 1730695187429, "contentHash": "100b8a33b917da1147740d7ab8b0ded3", "sourceName": "lib/forge-std/src/console.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console": {"0.8.12": {"path": "console.sol/console.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/console2.sol": {"lastModificationDate": 1730695187430, "contentHash": "491ca717c1915995e78cc361485a3067", "sourceName": "lib/forge-std/src/console2.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console2": {"0.8.12": {"path": "console2.sol/console2.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IERC165.sol": {"lastModificationDate": 1730695187432, "contentHash": "90fe5e2e3ed432d6f3b408e7c9e8a739", "sourceName": "lib/forge-std/src/interfaces/IERC165.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2", "artifacts": {"IERC165": {"0.8.12": {"path": "IERC165.sol/IERC165.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IERC20.sol": {"lastModificationDate": 1730695187432, "contentHash": "8099161d518e5862a76750349d58e801", "sourceName": "lib/forge-std/src/interfaces/IERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2", "artifacts": {"IERC20": {"0.8.12": {"path": "IERC20.sol/IERC20.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IERC721.sol": {"lastModificationDate": 1730695187433, "contentHash": "efc26e7f9a2f76b68088c8760ceae2dc", "sourceName": "lib/forge-std/src/interfaces/IERC721.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/interfaces/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC721": {"0.8.12": {"path": "IERC721.sol/IERC721.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "IERC721Enumerable": {"0.8.12": {"path": "IERC721.sol/IERC721Enumerable.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "IERC721Metadata": {"0.8.12": {"path": "IERC721.sol/IERC721Metadata.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "IERC721TokenReceiver": {"0.8.12": {"path": "IERC721.sol/IERC721TokenReceiver.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"lastModificationDate": 1730695187434, "contentHash": "7b131ca1ca32ef6378b7b9ad5488b901", "sourceName": "lib/forge-std/src/interfaces/IMulticall3.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IMulticall3": {"0.8.12": {"path": "IMulticall3.sol/IMulticall3.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/mocks/MockERC20.sol": {"lastModificationDate": 1730695187434, "contentHash": "8e14d63e81e1d54dbc2d44df38ae9dec", "sourceName": "lib/forge-std/src/mocks/MockERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/interfaces/IERC20.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"MockERC20": {"0.8.12": {"path": "MockERC20.sol/MockERC20.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/mocks/MockERC721.sol": {"lastModificationDate": 1730695187435, "contentHash": "58a77bb0832bf28b8edea8e830e21e63", "sourceName": "lib/forge-std/src/mocks/MockERC721.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC721.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IERC721TokenReceiver": {"0.8.12": {"path": "MockERC721.sol/IERC721TokenReceiver.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "MockERC721": {"0.8.12": {"path": "MockERC721.sol/MockERC721.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/forge-std/src/safeconsole.sol": {"lastModificationDate": 1730695187437, "contentHash": "ac3b1bf5a444db5db3656021830258a8", "sourceName": "lib/forge-std/src/safeconsole.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"safeconsole": {"0.8.12": {"path": "safeconsole.sol/safeconsole.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"lastModificationDate": 1730695187465, "contentHash": "e436cea06129be2c73cda4b1acc848b5", "sourceName": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"Ownable": {"0.8.12": {"path": "Ownable.sol/Ownable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol": {"lastModificationDate": 1730695187484, "contentHash": "8fe867b95c856b204f954a1910e28a1e", "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1271": {"0.8.12": {"path": "IERC1271.sol/IERC1271.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol": {"lastModificationDate": 1730695187511, "contentHash": "2858d98e74e67987ec81b39605230b74", "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1822Proxiable": {"0.8.12": {"path": "draft-IERC1822.sol/IERC1822Proxiable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"lastModificationDate": 1730695187552, "contentHash": "3fc3c7c0a2956f36e766691bb9473b06", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC1967Proxy": {"0.8.12": {"path": "ERC1967Proxy.sol/ERC1967Proxy.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol": {"lastModificationDate": 1730695187552, "contentHash": "6baa887a798e95b14f34e093f117e9b2", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.2", "artifacts": {"ERC1967Upgrade": {"0.8.12": {"path": "ERC1967Upgrade.sol/ERC1967Upgrade.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"lastModificationDate": 1730695187553, "contentHash": "40b3d81a836d50ff47e03893dcaaf204", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Proxy": {"0.8.12": {"path": "Proxy.sol/Proxy.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol": {"lastModificationDate": 1730695187553, "contentHash": "dc9dcb6e542154d9cfbfaece646c1092", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BeaconProxy": {"0.8.12": {"path": "BeaconProxy.sol/BeaconProxy.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"lastModificationDate": 1730695187554, "contentHash": "b6bd23bf19e90b771337037706470933", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IBeacon": {"0.8.12": {"path": "IBeacon.sol/IBeacon.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol": {"lastModificationDate": 1730695187554, "contentHash": "8ffefb755605824cf730ce4092b2f581", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"UpgradeableBeacon": {"0.8.12": {"path": "UpgradeableBeacon.sol/UpgradeableBeacon.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"lastModificationDate": 1730695187554, "contentHash": "a947492251ac15d6bfd899c9fdb4d82b", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ProxyAdmin": {"0.8.12": {"path": "ProxyAdmin.sol/ProxyAdmin.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"lastModificationDate": 1730695187555, "contentHash": "ea48b4a63fd733eec048191be006daa8", "sourceName": "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.0", "artifacts": {"TransparentUpgradeableProxy": {"0.8.12": {"path": "TransparentUpgradeableProxy.sol/TransparentUpgradeableProxy.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1730695187561, "contentHash": "af7bd64e1cfefbf6cb07f2adc1a25392", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20": {"0.8.12": {"path": "ERC20.sol/ERC20.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1730695187562, "contentHash": "ad7c2d0af148c8f9f097d65deeb4da6b", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20": {"0.8.12": {"path": "ERC20/IERC20.sol/IERC20.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"lastModificationDate": 1730695187562, "contentHash": "a1c7f80ae26f5b2d7d563475627fbf25", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20Burnable": {"0.8.12": {"path": "ERC20Burnable.sol/ERC20Burnable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1730695187567, "contentHash": "909ab67fc5c25033fe6cd364f8c056f9", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Metadata": {"0.8.12": {"path": "IERC20Metadata.sol/IERC20Metadata.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol": {"lastModificationDate": 1730695187568, "contentHash": "fb77f144244b9ab12533aa6ce85ef8c5", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Permit": {"0.8.12": {"path": "draft-IERC20Permit.sol/IERC20Permit.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol": {"lastModificationDate": 1730695187568, "contentHash": "d4d35c0977f3c87ab399a75a45d6fc19", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20PresetFixedSupply": {"0.8.12": {"path": "ERC20PresetFixedSupply.sol/ERC20PresetFixedSupply.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"lastModificationDate": 1730695187570, "contentHash": "3a843b05b85a270e9455e3d2e804e633", "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol"], "versionRequirement": "^0.8.0", "artifacts": {"SafeERC20": {"0.8.12": {"path": "SafeERC20.sol/SafeERC20.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"lastModificationDate": 1730695187579, "contentHash": "c476b3895a94798b88a4bb97399e6dfe", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.1", "artifacts": {"Address": {"0.8.12": {"path": "Address.sol/Address.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"lastModificationDate": 1730695187580, "contentHash": "5f2c5c4b6af2dd4551027144797bc8be", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Context": {"0.8.12": {"path": "Context.sol/Context.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Create2.sol": {"lastModificationDate": 1730695187580, "contentHash": "8932e2855e9aac6e79af79e499863b10", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Create2": {"0.8.12": {"path": "Create2.sol/Create2.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1730695187581, "contentHash": "f993f8f50186952a59ee5e3a30b68222", "sourceName": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"StorageSlot": {"0.8.12": {"path": "StorageSlot.sol/StorageSlot.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"lastModificationDate": 1730695187581, "contentHash": "cf46906c4035f51639a22265066a9e78", "sourceName": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Strings": {"0.8.12": {"path": "Strings.sol/Strings.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1730695187582, "contentHash": "1dfb7cf7c7e2edae73403d50a59cc967", "sourceName": "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Strings.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ECDSA": {"0.8.12": {"path": "ECDSA.sol/ECDSA.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol": {"lastModificationDate": 1730695187582, "contentHash": "161aae4dc1450371d0f324488f66a9cf", "sourceName": "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol"], "versionRequirement": "^0.8.0", "artifacts": {"EIP712": {"0.8.12": {"path": "draft-EIP712.sol/EIP712.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"lastModificationDate": 1730695187682, "contentHash": "403ce8273abde646bff81558ddf512ad", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"OwnableUpgradeable": {"0.8.12": {"path": "OwnableUpgradeable.sol/OwnableUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol": {"lastModificationDate": 1730695187696, "contentHash": "7e169522c39a2d02d418dc22a9f1aa1e", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC1271Upgradeable": {"0.8.12": {"path": "IERC1271Upgradeable.sol/IERC1271Upgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"lastModificationDate": 1730695187779, "contentHash": "b98e2f3a856e6e7f2106fb919bacab9e", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.2", "artifacts": {"Initializable": {"0.8.12": {"path": "Initializable.sol/Initializable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol": {"lastModificationDate": 1730695187781, "contentHash": "c9cde6037fc8b1fe0ef04b79149ba733", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ReentrancyGuardUpgradeable": {"0.8.12": {"path": "ReentrancyGuardUpgradeable.sol/ReentrancyGuardUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol": {"lastModificationDate": 1730695187803, "contentHash": "d42e87f4fba2b03ab4d3c14cb53d0c51", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.1", "artifacts": {"AddressUpgradeable": {"0.8.12": {"path": "AddressUpgradeable.sol/AddressUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol": {"lastModificationDate": 1730695187804, "contentHash": "4314aafe8c4cb988ebb9468321c3c63a", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CheckpointsUpgradeable": {"0.8.12": {"path": "CheckpointsUpgradeable.sol/CheckpointsUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"lastModificationDate": 1730695187804, "contentHash": "6200b84950eb05b4a92a39fd1d6e0f9b", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ContextUpgradeable": {"0.8.12": {"path": "ContextUpgradeable.sol/ContextUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol": {"lastModificationDate": 1730695187806, "contentHash": "b9d00086379b2524eb287c850a1b2e54", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"StringsUpgradeable": {"0.8.12": {"path": "StringsUpgradeable.sol/StringsUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol": {"lastModificationDate": 1730695187807, "contentHash": "c6eab8461f78befdc423f1d520ea5a97", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ECDSAUpgradeable": {"0.8.12": {"path": "ECDSAUpgradeable.sol/ECDSAUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol": {"lastModificationDate": 1730695187807, "contentHash": "e67f37d16bdf7ea0cb3ec187f734b396", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol"], "versionRequirement": "^0.8.0", "artifacts": {"SignatureCheckerUpgradeable": {"0.8.12": {"path": "SignatureCheckerUpgradeable.sol/SignatureCheckerUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol": {"lastModificationDate": 1730695187812, "contentHash": "bd2782c8789a3d3084d67c351a55ab92", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"MathUpgradeable": {"0.8.12": {"path": "MathUpgradeable.sol/MathUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol": {"lastModificationDate": 1730695187812, "contentHash": "00d9c0b8534f648176c53b50a914c19a", "sourceName": "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"SafeCastUpgradeable": {"0.8.12": {"path": "SafeCastUpgradeable.sol/SafeCastUpgradeable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "script/ServiceManagerRouterDeploy.s.sol": {"lastModificationDate": 1730695187899, "contentHash": "d2a7e5242bcfe685aa771d9cdfbb084d", "sourceName": "script/ServiceManagerRouterDeploy.s.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "src/ServiceManagerRouter.sol", "src/interfaces/IServiceManagerUI.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerRouterDeploy": {"0.8.12": {"path": "ServiceManagerRouterDeploy.s.sol/ServiceManagerRouterDeploy.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/BLSApkRegistry.sol": {"lastModificationDate": 1730695187900, "contentHash": "1711b0b6d5ad0b16f0cc351bac012440", "sourceName": "src/BLSApkRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/BLSApkRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSApkRegistry": {"0.8.12": {"path": "BLSApkRegistry.sol/BLSApkRegistry.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/BLSApkRegistryStorage.sol": {"lastModificationDate": 1730732613470, "contentHash": "5f745577909fc8c4b30e7472b26cb694", "sourceName": "src/BLSApkRegistryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSApkRegistryStorage": {"0.8.12": {"path": "BLSApkRegistryStorage.sol/BLSApkRegistryStorage.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/BLSSignatureChecker.sol": {"lastModificationDate": 1730695187901, "contentHash": "0ef85f18c35961836a9fe6b4d45ae122", "sourceName": "src/BLSSignatureChecker.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IBLSSignatureChecker.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSSignatureChecker": {"0.8.12": {"path": "BLSSignatureChecker.sol/BLSSignatureChecker.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/EjectionManager.sol": {"lastModificationDate": 1730732612643, "contentHash": "6c0dc3103c8f46f41af3c75bcae1ca80", "sourceName": "src/EjectionManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IEjectionManager.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EjectionManager": {"0.8.12": {"path": "EjectionManager.sol/EjectionManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/IndexRegistry.sol": {"lastModificationDate": 1730695187902, "contentHash": "5c85076686d53e3ccbc83fd0c20f48fa", "sourceName": "src/IndexRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/IndexRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IndexRegistry": {"0.8.12": {"path": "IndexRegistry.sol/IndexRegistry.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/IndexRegistryStorage.sol": {"lastModificationDate": 1730732613497, "contentHash": "39f1ece351bd387cfa441975307c9442", "sourceName": "src/IndexRegistryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IndexRegistryStorage": {"0.8.12": {"path": "IndexRegistryStorage.sol/IndexRegistryStorage.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/OperatorStateRetriever.sol": {"lastModificationDate": 1730695187903, "contentHash": "3e36f1b4028ea551ee90139356f85e2c", "sourceName": "src/OperatorStateRetriever.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"OperatorStateRetriever": {"0.8.12": {"path": "OperatorStateRetriever.sol/OperatorStateRetriever.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/RegistryCoordinator.sol": {"lastModificationDate": 1730732613856, "contentHash": "8c78acb60ea81555b8fc49cfb9ea3dfd", "sourceName": "src/RegistryCoordinator.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/RegistryCoordinatorStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RegistryCoordinator": {"0.8.12": {"path": "RegistryCoordinator.sol/RegistryCoordinator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/RegistryCoordinatorStorage.sol": {"lastModificationDate": 1730695187904, "contentHash": "11d8577be08b26fd3d9e9b18e30fe5ab", "sourceName": "src/RegistryCoordinatorStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RegistryCoordinatorStorage": {"0.8.12": {"path": "RegistryCoordinatorStorage.sol/RegistryCoordinatorStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/ServiceManagerBase.sol": {"lastModificationDate": 1730732613658, "contentHash": "24aacffa41919fa43818aae47e36f19b", "sourceName": "src/ServiceManagerBase.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/ServiceManagerBaseStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerBase": {"0.8.12": {"path": "ServiceManagerBase.sol/ServiceManagerBase.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/ServiceManagerBaseStorage.sol": {"lastModificationDate": 1730732613479, "contentHash": "244abda859f18c09adf63ca0141536b1", "sourceName": "src/ServiceManagerBaseStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerBaseStorage": {"0.8.12": {"path": "ServiceManagerBaseStorage.sol/ServiceManagerBaseStorage.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/ServiceManagerRouter.sol": {"lastModificationDate": 1730695187905, "contentHash": "a7a368ad5f9de364eb6ca9784717e194", "sourceName": "src/ServiceManagerRouter.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "src/interfaces/IServiceManagerUI.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerRouter": {"0.8.12": {"path": "ServiceManagerRouter.sol/ServiceManagerRouter.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/StakeRegistry.sol": {"lastModificationDate": 1730695187905, "contentHash": "c396b1e9938240782725040cc8f93ef9", "sourceName": "src/StakeRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StakeRegistry": {"0.8.12": {"path": "StakeRegistry.sol/StakeRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/StakeRegistryStorage.sol": {"lastModificationDate": 1730695187906, "contentHash": "433b43f1037e2b91c6d9851756f62106", "sourceName": "src/StakeRegistryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StakeRegistryStorage": {"0.8.12": {"path": "StakeRegistryStorage.sol/StakeRegistryStorage.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IBLSApkRegistry.sol": {"lastModificationDate": 1730695187906, "contentHash": "c43042a8b1a34d556af10d0ff4226c44", "sourceName": "src/interfaces/IBLSApkRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/interfaces/IRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IBLSApkRegistry": {"0.8.12": {"path": "IBLSApkRegistry.sol/IBLSApkRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IBLSSignatureChecker.sol": {"lastModificationDate": 1730695187906, "contentHash": "af3858f4678c3dee8362257dc08ff8b2", "sourceName": "src/interfaces/IBLSSignatureChecker.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IBLSSignatureChecker": {"0.8.12": {"path": "IBLSSignatureChecker.sol/IBLSSignatureChecker.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol": {"lastModificationDate": 1730695187907, "contentHash": "53030e6e4e1e8c942b2aab4b9cca4b7a", "sourceName": "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistryEventsAndErrors": {"0.8.12": {"path": "IECDSAStakeRegistryEventsAndErrors.sol/ECDSAStakeRegistryEventsAndErrors.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IEjectionManager.sol": {"lastModificationDate": 1730695187907, "contentHash": "4cd3c2936fe7c1c6d8bc23df8e644998", "sourceName": "src/interfaces/IEjectionManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IEjectionManager": {"0.8.12": {"path": "IEjectionManager.sol/IEjectionManager.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IIndexRegistry.sol": {"lastModificationDate": 1730695187907, "contentHash": "d54e4e739c33467dbcf0f3a3b1766ef0", "sourceName": "src/interfaces/IIndexRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/interfaces/IRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IIndexRegistry": {"0.8.12": {"path": "IIndexRegistry.sol/IIndexRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IRegistry.sol": {"lastModificationDate": 1730695187908, "contentHash": "8c3eb97f4b40d63ac7783d80d57a4e78", "sourceName": "src/interfaces/IRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"IRegistry": {"0.8.12": {"path": "IRegistry.sol/IRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IRegistryCoordinator.sol": {"lastModificationDate": 1730695187908, "contentHash": "61521d21514f328d85827e1415c81b6d", "sourceName": "src/interfaces/IRegistryCoordinator.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IRegistryCoordinator": {"0.8.12": {"path": "IRegistryCoordinator.sol/IRegistryCoordinator.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IServiceManager.sol": {"lastModificationDate": 1730695187908, "contentHash": "8332776785d695ed6980fb4e7a12ebde", "sourceName": "src/interfaces/IServiceManager.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IServiceManagerUI.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IServiceManager": {"0.8.12": {"path": "IServiceManager.sol/IServiceManager.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IServiceManagerUI.sol": {"lastModificationDate": 1730695187908, "contentHash": "864835546c5907425894ab6adecf2065", "sourceName": "src/interfaces/IServiceManagerUI.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol"], "versionRequirement": ">=0.5.0", "artifacts": {"IServiceManagerUI": {"0.8.12": {"path": "IServiceManagerUI.sol/IServiceManagerUI.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/ISocketUpdater.sol": {"lastModificationDate": 1730695187909, "contentHash": "711e6b611bf83a84bb9f501221b044d7", "sourceName": "src/interfaces/ISocketUpdater.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"ISocketUpdater": {"0.8.12": {"path": "ISocketUpdater.sol/ISocketUpdater.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/interfaces/IStakeRegistry.sol": {"lastModificationDate": 1730695187909, "contentHash": "31c44d66f9d90e60b858555ac7cef6a4", "sourceName": "src/interfaces/IStakeRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IStakeRegistry": {"0.8.12": {"path": "IStakeRegistry.sol/IStakeRegistry.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/libraries/BN254.sol": {"lastModificationDate": 1730695187909, "contentHash": "1ac87cd70494875f80347c3cc7d00b32", "sourceName": "src/libraries/BN254.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"BN254": {"0.8.12": {"path": "BN254.sol/BN254.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/libraries/BitmapUtils.sol": {"lastModificationDate": 1730695187910, "contentHash": "cf3e333c52a925922555fd0b4aea4463", "sourceName": "src/libraries/BitmapUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"BitmapUtils": {"0.8.12": {"path": "BitmapUtils.sol/BitmapUtils.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "src/unaudited/ECDSAServiceManagerBase.sol": {"lastModificationDate": 1730732613497, "contentHash": "ceb8a5b2286912e69a58feb71ea7a806", "sourceName": "src/unaudited/ECDSAServiceManagerBase.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAServiceManagerBase": {"0.8.12": {"path": "ECDSAServiceManagerBase.sol/ECDSAServiceManagerBase.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/unaudited/ECDSAStakeRegistry.sol": {"lastModificationDate": 1730732614101, "contentHash": "4bb347fdd142376f39bf1d172cc4f717", "sourceName": "src/unaudited/ECDSAStakeRegistry.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistry": {"0.8.12": {"path": "ECDSAStakeRegistry.sol/ECDSAStakeRegistry.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/unaudited/ECDSAStakeRegistryStorage.sol": {"lastModificationDate": 1730732613856, "contentHash": "218ffc4e3abd93236eab3e0f2c5c6a5d", "sourceName": "src/unaudited/ECDSAStakeRegistryStorage.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistryStorage": {"0.8.12": {"path": "ECDSAStakeRegistryStorage.sol/ECDSAStakeRegistryStorage.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/unaudited/examples/ECDSAStakeRegistryEqualWeight.sol": {"lastModificationDate": 1730732614101, "contentHash": "f095725e4bd28583db99ae479cca189a", "sourceName": "src/unaudited/examples/ECDSAStakeRegistryEqualWeight.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol", "src/unaudited/examples/ECDSAStakeRegistryPermissioned.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistryEqualWeight": {"0.8.12": {"path": "ECDSAStakeRegistryEqualWeight.sol/ECDSAStakeRegistryEqualWeight.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "src/unaudited/examples/ECDSAStakeRegistryPermissioned.sol": {"lastModificationDate": 1730695187913, "contentHash": "003f4983621c485d96f947e6a46fef60", "sourceName": "src/unaudited/examples/ECDSAStakeRegistryPermissioned.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistryPermissioned": {"0.8.12": {"path": "ECDSAStakeRegistryPermissioned.sol/ECDSAStakeRegistryPermissioned.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/events/IBLSApkRegistryEvents.sol": {"lastModificationDate": 1730695187913, "contentHash": "da13a3390edbc92f9e8b839290c717e1", "sourceName": "test/events/IBLSApkRegistryEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IBLSApkRegistryEvents": {"0.8.12": {"path": "IBLSApkRegistryEvents.sol/IBLSApkRegistryEvents.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/events/IIndexRegistryEvents.sol": {"lastModificationDate": 1730695187913, "contentHash": "22d82889c7a81d4fb936c5738c2b737a", "sourceName": "test/events/IIndexRegistryEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"IIndexRegistryEvents": {"0.8.12": {"path": "IIndexRegistryEvents.sol/IIndexRegistryEvents.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/events/IServiceManagerBaseEvents.sol": {"lastModificationDate": 1730695187913, "contentHash": "35d4f05b5c2451d6a996d8d93b4ec48a", "sourceName": "test/events/IServiceManagerBaseEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IServiceManagerBaseEvents": {"0.8.12": {"path": "IServiceManagerBaseEvents.sol/IServiceManagerBaseEvents.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/events/IStakeRegistryEvents.sol": {"lastModificationDate": 1730695187914, "contentHash": "106547d87d4f8998421cb0e8ce023873", "sourceName": "test/events/IStakeRegistryEvents.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IStakeRegistry.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IStakeRegistryEvents": {"0.8.12": {"path": "IStakeRegistryEvents.sol/IStakeRegistryEvents.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/ffi/BLSPubKeyCompendiumFFI.t.sol": {"lastModificationDate": 1730695187914, "contentHash": "********************************", "sourceName": "test/ffi/BLSPubKeyCompendiumFFI.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "test/ffi/util/G2Operations.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSApkRegistryFFITests": {"0.8.12": {"path": "BLSPubKeyCompendiumFFI.t.sol/BLSApkRegistryFFITests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/ffi/BLSSignatureCheckerFFI.t.sol": {"lastModificationDate": 1730695187914, "contentHash": "ed2268bd82b8235ed663e37aac17372a", "sourceName": "test/ffi/BLSSignatureCheckerFFI.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/BLSSignatureChecker.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IBLSSignatureChecker.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSSignatureCheckerFFITests": {"0.8.12": {"path": "BLSSignatureCheckerFFI.t.sol/BLSSignatureCheckerFFITests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/ffi/UpdateOperators.t.sol": {"lastModificationDate": 1730695187915, "contentHash": "c553b28ec587894119f118ed1c77f877", "sourceName": "test/ffi/UpdateOperators.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationBase.t.sol", "test/integration/IntegrationChecks.t.sol", "test/integration/IntegrationConfig.t.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_AVS_Sync_GasCosts_FFI": {"0.8.12": {"path": "UpdateOperators.t.sol/Integration_AVS_Sync_GasCosts_FFI.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/ffi/util/G2Operations.sol": {"lastModificationDate": 1730695187916, "contentHash": "9814c55a4ec09d5983000039e48670f3", "sourceName": "test/ffi/util/G2Operations.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"G2Operations": {"0.8.12": {"path": "G2Operations.sol/G2Operations.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/harnesses/BLSApkRegistryHarness.sol": {"lastModificationDate": 1730695187916, "contentHash": "062f4afff0c07ebe0fd331c1271f80dd", "sourceName": "test/harnesses/BLSApkRegistryHarness.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSApkRegistryHarness": {"0.8.12": {"path": "BLSApkRegistryHarness.sol/BLSApkRegistryHarness.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/harnesses/BitmapUtilsWrapper.sol": {"lastModificationDate": 1730695187917, "contentHash": "25fa47a9d015acfd11d165c7adea9933", "sourceName": "test/harnesses/BitmapUtilsWrapper.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BitmapUtilsWrapper": {"0.8.12": {"path": "BitmapUtilsWrapper.sol/BitmapUtilsWrapper.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/harnesses/RegistryCoordinatorHarness.t.sol": {"lastModificationDate": 1730695187917, "contentHash": "efc3b2a5995d3bf9490a58206c496c2a", "sourceName": "test/harnesses/RegistryCoordinatorHarness.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RegistryCoordinatorHarness": {"0.8.12": {"path": "RegistryCoordinatorHarness.t.sol/RegistryCoordinatorHarness.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/harnesses/StakeRegistryHarness.sol": {"lastModificationDate": 1730695187917, "contentHash": "f1d81c6caed1f8a786aa2972fd2118b6", "sourceName": "test/harnesses/StakeRegistryHarness.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StakeRegistryHarness": {"0.8.12": {"path": "StakeRegistryHarness.sol/StakeRegistryHarness.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/integration/CoreRegistration.t.sol": {"lastModificationDate": 1730695187917, "contentHash": "874afd3e906506e0306af634f733e9c3", "sourceName": "test/integration/CoreRegistration.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Test_CoreRegistration": {"0.8.12": {"path": "CoreRegistration.t.sol/Test_CoreRegistration.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/IntegrationBase.t.sol": {"lastModificationDate": 1730695187918, "contentHash": "ba79057e7beea528e099d76536e9471f", "sourceName": "test/integration/IntegrationBase.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationConfig.t.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationBase": {"0.8.12": {"path": "IntegrationBase.t.sol/IntegrationBase.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/IntegrationChecks.t.sol": {"lastModificationDate": 1730695187921, "contentHash": "9a4816bd0f33bbee942ace6f7c2fdb37", "sourceName": "test/integration/IntegrationChecks.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationBase.t.sol", "test/integration/IntegrationConfig.t.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationChecks": {"0.8.12": {"path": "IntegrationChecks.t.sol/IntegrationChecks.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/IntegrationConfig.t.sol": {"lastModificationDate": 1730695187921, "contentHash": "c856c1ae506548481241e34d3cb3410d", "sourceName": "test/integration/IntegrationConfig.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Constants": {"0.8.12": {"path": "IntegrationConfig.t.sol/Constants.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "IntegrationConfig": {"0.8.12": {"path": "IntegrationConfig.t.sol/IntegrationConfig.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/IntegrationDeployer.t.sol": {"lastModificationDate": 1730695187922, "contentHash": "4d1fff36fc2d2abfa4689179e2f9b07a", "sourceName": "test/integration/IntegrationDeployer.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IntegrationDeployer": {"0.8.12": {"path": "IntegrationDeployer.t.sol/IntegrationDeployer.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/TimeMachine.t.sol": {"lastModificationDate": 1730695187923, "contentHash": "51a6fcf865f7cabba81bc0231d8a74ac", "sourceName": "test/integration/TimeMachine.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": "^0.8.12", "artifacts": {"TimeMachine": {"0.8.12": {"path": "TimeMachine.t.sol/TimeMachine.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/integration/User.t.sol": {"lastModificationDate": 1730695187923, "contentHash": "69c04551c271a4a76c6e2c4fd37304d0", "sourceName": "test/integration/User.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/integration/TimeMachine.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IUserDeployer": {"0.8.12": {"path": "User.t.sol/IUserDeployer.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "User": {"0.8.12": {"path": "User.t.sol/User.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "User_AltMethods": {"0.8.12": {"path": "User.t.sol/User_AltMethods.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/tests/Full_Register_Deregister.t.sol": {"lastModificationDate": 1730695187924, "contentHash": "5359662e189a71a49b33979fc4c52004", "sourceName": "test/integration/tests/Full_Register_Deregister.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationBase.t.sol", "test/integration/IntegrationChecks.t.sol", "test/integration/IntegrationConfig.t.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_Full_Register_Deregister": {"0.8.12": {"path": "Full_Register_Deregister.t.sol/Integration_Full_Register_Deregister.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/tests/NonFull_Register_CoreBalanceChange_Update.t.sol": {"lastModificationDate": 1730695187924, "contentHash": "7c2225b05f7ff2bca7d9654c04732cfb", "sourceName": "test/integration/tests/NonFull_Register_CoreBalanceChange_Update.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationBase.t.sol", "test/integration/IntegrationChecks.t.sol", "test/integration/IntegrationConfig.t.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_NonFull_Register_CoreBalanceChange_Update": {"0.8.12": {"path": "NonFull_Register_CoreBalanceChange_Update.t.sol/Integration_NonFull_Register_CoreBalanceChange_Update.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/tests/NonFull_Register_Deregister.t.sol": {"lastModificationDate": 1730695187925, "contentHash": "0b3e8d882153c777c294ab9034656469", "sourceName": "test/integration/tests/NonFull_Register_Deregister.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/core/DelegationManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BytesLib.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPod.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodPausingConstants.sol", "lib/eigenlayer-contracts/src/contracts/pods/EigenPodStorage.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/ETHDepositMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/ffi/util/G2Operations.sol", "test/integration/IntegrationBase.t.sol", "test/integration/IntegrationChecks.t.sol", "test/integration/IntegrationConfig.t.sol", "test/integration/IntegrationDeployer.t.sol", "test/integration/TimeMachine.t.sol", "test/integration/User.t.sol", "test/integration/utils/BitmapStrings.t.sol", "test/integration/utils/Sort.t.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Integration_NonFull_Register_Deregister": {"0.8.12": {"path": "NonFull_Register_Deregister.t.sol/Integration_NonFull_Register_Deregister.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/integration/utils/BitmapStrings.t.sol": {"lastModificationDate": 1730695187925, "contentHash": "bca8b126c662a7e51f417671945511d1", "sourceName": "test/integration/utils/BitmapStrings.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Strings.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BitmapStrings": {"0.8.12": {"path": "BitmapStrings.t.sol/BitmapStrings.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/integration/utils/Sort.t.sol": {"lastModificationDate": 1730695187926, "contentHash": "fe1d4f81d8d817c8a25d1c27ce15c817", "sourceName": "test/integration/utils/Sort.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"Sort": {"0.8.12": {"path": "Sort.t.sol/Sort.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/mocks/AVSDirectoryMock.sol": {"lastModificationDate": 1730695187926, "contentHash": "55d88381eb0dc71d986315686ad3b989", "sourceName": "test/mocks/AVSDirectoryMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"AVSDirectoryMock": {"0.8.12": {"path": "AVSDirectoryMock.sol/AVSDirectoryMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/mocks/DelegationMock.sol": {"lastModificationDate": 1730695187927, "contentHash": "6549836a706c9b24fecfe57c59da7468", "sourceName": "test/mocks/DelegationMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"DelegationMock": {"0.8.12": {"path": "DelegationMock.sol/DelegationMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/mocks/ECDSAServiceManagerMock.sol": {"lastModificationDate": 1730695187927, "contentHash": "edba03d899bfb9edf9d5bff234a90a17", "sourceName": "test/mocks/ECDSAServiceManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/unaudited/ECDSAServiceManagerBase.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAServiceManagerMock": {"0.8.12": {"path": "ECDSAServiceManagerMock.sol/ECDSAServiceManagerMock.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/mocks/ECDSAStakeRegistryMock.sol": {"lastModificationDate": 1730695187927, "contentHash": "c7ecd2fcc4ad94ddbca1d8fe6bb96819", "sourceName": "test/mocks/ECDSAStakeRegistryMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistryMock": {"0.8.12": {"path": "ECDSAStakeRegistryMock.sol/ECDSAStakeRegistryMock.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/mocks/RegistryCoordinatorMock.sol": {"lastModificationDate": 1730695187927, "contentHash": "f3508207ab27d67877d0d6d5ba41f29a", "sourceName": "test/mocks/RegistryCoordinatorMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RegistryCoordinatorMock": {"0.8.12": {"path": "RegistryCoordinatorMock.sol/RegistryCoordinatorMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/mocks/RewardsCoordinatorMock.sol": {"lastModificationDate": 1730695187928, "contentHash": "dd2418ae2138909ec066d9582133be48", "sourceName": "test/mocks/RewardsCoordinatorMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RewardsCoordinatorMock": {"0.8.12": {"path": "RewardsCoordinatorMock.sol/RewardsCoordinatorMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/mocks/ServiceManagerMock.sol": {"lastModificationDate": 1730695187928, "contentHash": "4327dd60f9c8a58d0b5ebd757d1606e5", "sourceName": "test/mocks/ServiceManagerMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerMock": {"0.8.12": {"path": "ServiceManagerMock.sol/ServiceManagerMock.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/mocks/StakeRegistryMock.sol": {"lastModificationDate": 1730695187928, "contentHash": "b2875d2858f04884e328192f3055f0b4", "sourceName": "test/mocks/StakeRegistryMock.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StakeRegistryMock": {"0.8.12": {"path": "StakeRegistryMock.sol/StakeRegistryMock.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/unit/BLSApkRegistryUnit.t.sol": {"lastModificationDate": 1730695187931, "contentHash": "4779f7848084a23c78dd61eb6ed9ad75", "sourceName": "test/unit/BLSApkRegistryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/BLSSignatureChecker.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IBLSSignatureChecker.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/events/IBLSApkRegistryEvents.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/BitmapUtilsWrapper.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RegistryCoordinatorMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/BLSMockAVSDeployer.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSApkRegistryUnitTests": {"0.8.12": {"path": "BLSApkRegistryUnit.t.sol/BLSApkRegistryUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "BLSApkRegistryUnitTests_configAndGetters": {"0.8.12": {"path": "BLSApkRegistryUnit.t.sol/BLSApkRegistryUnitTests_configAndGetters.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "BLSApkRegistryUnitTests_deregisterOperator": {"0.8.12": {"path": "BLSApkRegistryUnit.t.sol/BLSApkRegistryUnitTests_deregisterOperator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "BLSApkRegistryUnitTests_quorumApkUpdates": {"0.8.12": {"path": "BLSApkRegistryUnit.t.sol/BLSApkRegistryUnitTests_quorumApkUpdates.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "BLSApkRegistryUnitTests_registerBLSPublicKey": {"0.8.12": {"path": "BLSApkRegistryUnit.t.sol/BLSApkRegistryUnitTests_registerBLSPublicKey.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "BLSApkRegistryUnitTests_registerOperator": {"0.8.12": {"path": "BLSApkRegistryUnit.t.sol/BLSApkRegistryUnitTests_registerOperator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/BLSSignatureCheckerUnit.t.sol": {"lastModificationDate": 1730695187932, "contentHash": "d45745b3785fdb3dcf7bead20a1ba3ec", "sourceName": "test/unit/BLSSignatureCheckerUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/BLSSignatureChecker.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IBLSSignatureChecker.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/BLSMockAVSDeployer.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSSignatureCheckerUnitTests": {"0.8.12": {"path": "BLSSignatureCheckerUnit.t.sol/BLSSignatureCheckerUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/BitmapUtils.t.sol": {"lastModificationDate": 1730695187932, "contentHash": "62dac01274dc12c5990b51b01e1aa276", "sourceName": "test/unit/BitmapUtils.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BitmapUtilsWrapper.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BitmapUtilsUnitTests": {"0.8.12": {"path": "BitmapUtils.t.sol/BitmapUtilsUnitTests.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "BitmapUtilsUnitTests_bitwiseOperations": {"0.8.12": {"path": "BitmapUtils.t.sol/BitmapUtilsUnitTests_bitwiseOperations.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "BitmapUtilsUnitTests_bytesArrayToBitmap": {"0.8.12": {"path": "BitmapUtils.t.sol/BitmapUtilsUnitTests_bytesArrayToBitmap.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "BitmapUtilsUnitTests_isArrayStrictlyAscendingOrdered": {"0.8.12": {"path": "BitmapUtils.t.sol/BitmapUtilsUnitTests_isArrayStrictlyAscendingOrdered.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/unit/ECDSAServiceManager.t.sol": {"lastModificationDate": 1730695187932, "contentHash": "f9c0c59ffec5db332b37b4ca7e9bd774", "sourceName": "test/unit/ECDSAServiceManager.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/IStakeRegistry.sol", "src/unaudited/ECDSAServiceManagerBase.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol", "test/mocks/ECDSAServiceManagerMock.sol", "test/mocks/ECDSAStakeRegistryMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAServiceManagerSetup": {"0.8.12": {"path": "ECDSAServiceManager.t.sol/ECDSAServiceManagerSetup.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "MockAVSDirectory": {"0.8.12": {"path": "ECDSAServiceManager.t.sol/MockAVSDirectory.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "MockDelegationManager": {"0.8.12": {"path": "ECDSAServiceManager.t.sol/MockDelegationManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "MockRewardsCoordinator": {"0.8.12": {"path": "ECDSAServiceManager.t.sol/MockRewardsCoordinator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/ECDSAStakeRegistryEqualWeightUnit.t.sol": {"lastModificationDate": 1730695187933, "contentHash": "8690374cba467eee6be70dfe56d30256", "sourceName": "test/unit/ECDSAStakeRegistryEqualWeightUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol", "src/unaudited/examples/ECDSAStakeRegistryEqualWeight.sol", "src/unaudited/examples/ECDSAStakeRegistryPermissioned.sol", "test/unit/ECDSAStakeRegistryUnit.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EqualWeightECDSARegistry": {"0.8.12": {"path": "ECDSAStakeRegistryEqualWeightUnit.t.sol/EqualWeightECDSARegistry.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/ECDSAStakeRegistryPermissionedUnit.t.sol": {"lastModificationDate": 1730695187933, "contentHash": "8eb273a89cc77f47b7a87e084a1dea19", "sourceName": "test/unit/ECDSAStakeRegistryPermissionedUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol", "src/unaudited/examples/ECDSAStakeRegistryPermissioned.sol", "test/unit/ECDSAStakeRegistryUnit.t.sol"], "versionRequirement": "^0.8.12", "artifacts": {"PermissionedECDSAStakeRegistryTest": {"0.8.12": {"path": "ECDSAStakeRegistryPermissionedUnit.t.sol/PermissionedECDSAStakeRegistryTest.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/ECDSAStakeRegistryUnit.t.sol": {"lastModificationDate": 1730695187933, "contentHash": "8cc614fae624bfdab26459abd10775c3", "sourceName": "test/unit/ECDSAStakeRegistryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/interfaces/IERC1271Upgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/CheckpointsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/StringsUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/ECDSAUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/SignatureCheckerUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/MathUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/math/SafeCastUpgradeable.sol", "src/interfaces/IECDSAStakeRegistryEventsAndErrors.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/unaudited/ECDSAStakeRegistry.sol", "src/unaudited/ECDSAStakeRegistryStorage.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ECDSAStakeRegistrySetup": {"0.8.12": {"path": "ECDSAStakeRegistryUnit.t.sol/ECDSAStakeRegistrySetup.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "ECDSAStakeRegistryTest": {"0.8.12": {"path": "ECDSAStakeRegistryUnit.t.sol/ECDSAStakeRegistryTest.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "MockDelegationManager": {"0.8.12": {"path": "ECDSAStakeRegistryUnit.t.sol/MockDelegationManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "MockServiceManager": {"0.8.12": {"path": "ECDSAStakeRegistryUnit.t.sol/MockServiceManager.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/EjectionManagerUnit.t.sol": {"lastModificationDate": 1730695187934, "contentHash": "d6f60686d5d253156da959f1f453d0c3", "sourceName": "test/unit/EjectionManagerUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/EjectionManager.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IEjectionManager.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"EjectionManagerUnitTests": {"0.8.12": {"path": "EjectionManagerUnit.t.sol/EjectionManagerUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/IndexRegistryUnit.t.sol": {"lastModificationDate": 1730695187934, "contentHash": "ad2a4454bddf75166391c6a9d2476444", "sourceName": "test/unit/IndexRegistryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/events/IIndexRegistryEvents.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/BitmapUtilsWrapper.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"IndexRegistryUnitTests": {"0.8.12": {"path": "IndexRegistryUnit.t.sol/IndexRegistryUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "IndexRegistryUnitTests_configAndGetters": {"0.8.12": {"path": "IndexRegistryUnit.t.sol/IndexRegistryUnitTests_configAndGetters.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "IndexRegistryUnitTests_deregisterOperator": {"0.8.12": {"path": "IndexRegistryUnit.t.sol/IndexRegistryUnitTests_deregisterOperator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "IndexRegistryUnitTests_registerOperator": {"0.8.12": {"path": "IndexRegistryUnit.t.sol/IndexRegistryUnitTests_registerOperator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/OperatorStateRetrieverUnit.t.sol": {"lastModificationDate": 1730695187935, "contentHash": "650b0dd1b513df20c8c67768e2f345e4", "sourceName": "test/unit/OperatorStateRetrieverUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"OperatorStateRetrieverUnitTests": {"0.8.12": {"path": "OperatorStateRetrieverUnit.t.sol/OperatorStateRetrieverUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/RegistryCoordinatorUnit.t.sol": {"lastModificationDate": 1730695187935, "contentHash": "b64b87fd99f665a980a2526c17dc08e4", "sourceName": "test/unit/RegistryCoordinatorUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"RegistryCoordinatorUnitTests": {"0.8.12": {"path": "RegistryCoordinatorUnit.t.sol/RegistryCoordinatorUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "RegistryCoordinatorUnitTests_DeregisterOperator_EjectOperator": {"0.8.12": {"path": "RegistryCoordinatorUnit.t.sol/RegistryCoordinatorUnitTests_DeregisterOperator_EjectOperator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "RegistryCoordinatorUnitTests_Initialization_Setters": {"0.8.12": {"path": "RegistryCoordinatorUnit.t.sol/RegistryCoordinatorUnitTests_Initialization_Setters.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "RegistryCoordinatorUnitTests_RegisterOperator": {"0.8.12": {"path": "RegistryCoordinatorUnit.t.sol/RegistryCoordinatorUnitTests_RegisterOperator.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "RegistryCoordinatorUnitTests_RegisterOperatorWithChurn": {"0.8.12": {"path": "RegistryCoordinatorUnit.t.sol/RegistryCoordinatorUnitTests_RegisterOperatorWithChurn.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "RegistryCoordinatorUnitTests_UpdateOperators": {"0.8.12": {"path": "RegistryCoordinatorUnit.t.sol/RegistryCoordinatorUnitTests_UpdateOperators.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/ServiceManagerBase.t.sol": {"lastModificationDate": 1730695187935, "contentHash": "f8a0fdf600e55efd0fdcac6b97f39322", "sourceName": "test/unit/ServiceManagerBase.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/presets/ERC20PresetFixedSupply.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/events/IServiceManagerBaseEvents.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerBase_UnitTests": {"0.8.12": {"path": "ServiceManagerBase.t.sol/ServiceManagerBase_UnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/ServiceManagerRouter.t.sol": {"lastModificationDate": 1730695187936, "contentHash": "e5c758b77d3a1f7b73bb961904b51afa", "sourceName": "test/unit/ServiceManagerRouter.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/ServiceManagerRouter.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ServiceManagerRouter_UnitTests": {"0.8.12": {"path": "ServiceManagerRouter.t.sol/ServiceManagerRouter_UnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/StakeRegistryUnit.t.sol": {"lastModificationDate": 1730695187938, "contentHash": "611f6728c44c8c61d0c151f5ed578866", "sourceName": "test/unit/StakeRegistryUnit.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/events/IStakeRegistryEvents.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"StakeRegistryUnitTests": {"0.8.12": {"path": "StakeRegistryUnit.t.sol/StakeRegistryUnitTests.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "StakeRegistryUnitTests_Config": {"0.8.12": {"path": "StakeRegistryUnit.t.sol/StakeRegistryUnitTests_Config.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "StakeRegistryUnitTests_Deregister": {"0.8.12": {"path": "StakeRegistryUnit.t.sol/StakeRegistryUnitTests_Deregister.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "StakeRegistryUnitTests_Register": {"0.8.12": {"path": "StakeRegistryUnit.t.sol/StakeRegistryUnitTests_Register.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "StakeRegistryUnitTests_StakeUpdates": {"0.8.12": {"path": "StakeRegistryUnit.t.sol/StakeRegistryUnitTests_StakeUpdates.json", "build_id": "08064f56647916e490d2100b9a051e64"}}, "StakeRegistryUnitTests_weightOfOperatorForQuorum": {"0.8.12": {"path": "StakeRegistryUnit.t.sol/StakeRegistryUnitTests_weightOfOperatorForQuorum.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/unit/Utils.sol": {"lastModificationDate": 1730695187938, "contentHash": "11fd094e55821221a80c38fab6fdd979", "sourceName": "test/unit/Utils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/strategies/StrategyBase.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Utils": {"0.8.12": {"path": "Utils.sol/Utils.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/utils/BLSMockAVSDeployer.sol": {"lastModificationDate": 1730695187940, "contentHash": "315c072ce71dc9ce3e19a926cd6d5326", "sourceName": "test/utils/BLSMockAVSDeployer.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/BLSSignatureChecker.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IBLSSignatureChecker.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol", "test/utils/MockAVSDeployer.sol"], "versionRequirement": "^0.8.12", "artifacts": {"BLSMockAVSDeployer": {"0.8.12": {"path": "BLSMockAVSDeployer.sol/BLSMockAVSDeployer.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/utils/Greeter.sol": {"lastModificationDate": 1730695187941, "contentHash": "fb0302004ea8ac2cef53ce5fe2bfc967", "sourceName": "test/utils/Greeter.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"Greeter": {"0.8.12": {"path": "Greeter.sol/Greeter.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/GreeterProxiable.sol": {"lastModificationDate": 1730695187941, "contentHash": "********************************", "sourceName": "test/utils/GreeterProxiable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"GreeterProxiable": {"0.8.12": {"path": "GreeterProxiable.sol/GreeterProxiable.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "IERC1822Proxiable": {"0.8.12": {"path": "GreeterProxiable.sol/IERC1822Proxiable.json", "build_id": "c93b552cc771c6720da75394620666be"}}, "Proxiable": {"0.8.12": {"path": "GreeterProxiable.sol/Proxiable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/GreeterV2.sol": {"lastModificationDate": 1730695187941, "contentHash": "b942d689bdfdd3030a0e2496c1c49c3d", "sourceName": "test/utils/GreeterV2.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"GreeterV2": {"0.8.12": {"path": "GreeterV2.sol/GreeterV2.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/GreeterV2Proxiable.sol": {"lastModificationDate": 1730695187942, "contentHash": "7c4b2f6f89ddec8252d2723e24ccf459", "sourceName": "test/utils/GreeterV2Proxiable.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["test/utils/GreeterProxiable.sol"], "versionRequirement": "^0.8.12", "artifacts": {"GreeterV2Proxiable": {"0.8.12": {"path": "GreeterV2Proxiable.sol/GreeterV2Proxiable.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/MockAVSDeployer.sol": {"lastModificationDate": 1730695187942, "contentHash": "6ce11873c08f05766adaa76ceb04828e", "sourceName": "test/utils/MockAVSDeployer.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/eigenlayer-contracts/src/contracts/core/AVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/core/AVSDirectoryStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/core/RewardsCoordinatorStorage.sol", "lib/eigenlayer-contracts/src/contracts/core/Slasher.sol", "lib/eigenlayer-contracts/src/contracts/core/StrategyManagerStorage.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IAVSDirectory.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IDelegationManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IETHPOSDeposit.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPod.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IEigenPodManager.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPausable.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IPauserRegistry.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IRewardsCoordinator.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/ISlasher.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategy.sol", "lib/eigenlayer-contracts/src/contracts/interfaces/IStrategyManager.sol", "lib/eigenlayer-contracts/src/contracts/libraries/BeaconChainProofs.sol", "lib/eigenlayer-contracts/src/contracts/libraries/EIP1271SignatureUtils.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Endian.sol", "lib/eigenlayer-contracts/src/contracts/libraries/Merkle.sol", "lib/eigenlayer-contracts/src/contracts/permissions/Pausable.sol", "lib/eigenlayer-contracts/src/contracts/permissions/PauserRegistry.sol", "lib/eigenlayer-contracts/src/test/mocks/EigenPodManagerMock.sol", "lib/eigenlayer-contracts/src/test/mocks/EmptyContract.sol", "lib/eigenlayer-contracts/src/test/mocks/StrategyManagerMock.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/draft-IERC20Permit.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/draft-EIP712.sol", "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/security/ReentrancyGuardUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol", "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "src/BLSApkRegistry.sol", "src/BLSApkRegistryStorage.sol", "src/IndexRegistry.sol", "src/IndexRegistryStorage.sol", "src/OperatorStateRetriever.sol", "src/RegistryCoordinator.sol", "src/RegistryCoordinatorStorage.sol", "src/ServiceManagerBase.sol", "src/ServiceManagerBaseStorage.sol", "src/StakeRegistry.sol", "src/StakeRegistryStorage.sol", "src/interfaces/IBLSApkRegistry.sol", "src/interfaces/IIndexRegistry.sol", "src/interfaces/IRegistry.sol", "src/interfaces/IRegistryCoordinator.sol", "src/interfaces/IServiceManager.sol", "src/interfaces/IServiceManagerUI.sol", "src/interfaces/ISocketUpdater.sol", "src/interfaces/IStakeRegistry.sol", "src/libraries/BN254.sol", "src/libraries/BitmapUtils.sol", "test/harnesses/BLSApkRegistryHarness.sol", "test/harnesses/RegistryCoordinatorHarness.t.sol", "test/harnesses/StakeRegistryHarness.sol", "test/mocks/AVSDirectoryMock.sol", "test/mocks/DelegationMock.sol", "test/mocks/RewardsCoordinatorMock.sol", "test/mocks/ServiceManagerMock.sol"], "versionRequirement": "^0.8.12", "artifacts": {"MockAVSDeployer": {"0.8.12": {"path": "MockAVSDeployer.sol/MockAVSDeployer.json", "build_id": "08064f56647916e490d2100b9a051e64"}}}, "seenByCompiler": true}, "test/utils/NoInitializer.sol": {"lastModificationDate": 1730695187943, "contentHash": "c73c9c4a090d6635c0bcf0d139c29758", "sourceName": "test/utils/NoInitializer.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"NoInitializer": {"0.8.12": {"path": "NoInitializer.sol/NoInitializer.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/Operators.sol": {"lastModificationDate": 1730695187943, "contentHash": "106968ea97dd1773ce13e2ab07f2b8b9", "sourceName": "test/utils/Operators.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Operators": {"0.8.12": {"path": "Operators.sol/Operators.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/Owners.sol": {"lastModificationDate": 1730695187943, "contentHash": "729b6ea75b439a41cda0c284897f44c0", "sourceName": "test/utils/Owners.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": "^0.8.12", "artifacts": {"Owners": {"0.8.12": {"path": "Owners.sol/Owners.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/ProofParsing.sol": {"lastModificationDate": 1730695187944, "contentHash": "cdb0be5d326ab37f3fda2a7998623b67", "sourceName": "test/utils/ProofParsing.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "src/libraries/BN254.sol"], "versionRequirement": "^0.8.12", "artifacts": {"ProofParsing": {"0.8.12": {"path": "ProofParsing.sol/ProofParsing.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/ProxyTestContracts.sol": {"lastModificationDate": 1730695187944, "contentHash": "ff094adf4af8f8597c28e611edaf255b", "sourceName": "test/utils/ProxyTestContracts.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["test/utils/Greeter.sol", "test/utils/GreeterProxiable.sol", "test/utils/GreeterV2.sol", "test/utils/GreeterV2Proxiable.sol", "test/utils/NoInitializer.sol", "test/utils/WithConstructor.sol"], "versionRequirement": "^0.8.12", "artifacts": {}, "seenByCompiler": true}, "test/utils/SignatureCompaction.sol": {"lastModificationDate": 1730695187944, "contentHash": "27a84e2f29ceed4c3127cef9d635dcb5", "sourceName": "test/utils/SignatureCompaction.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol"], "versionRequirement": "^0.8.12", "artifacts": {"SignatureCompaction": {"0.8.12": {"path": "SignatureCompaction.sol/SignatureCompaction.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/UpgradeableProxyUtils.sol": {"lastModificationDate": 1730695187945, "contentHash": "6b307193301034651e5f8d47faa8cf2a", "sourceName": "test/utils/UpgradeableProxyUtils.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Vm.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.12", "artifacts": {"UpgradeableProxyUtils": {"0.8.12": {"path": "UpgradeableProxyUtils.sol/UpgradeableProxyUtils.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/UpgradeableProxyUtils.t.sol": {"lastModificationDate": 1730695187945, "contentHash": "56210479e32dd053a404cc42fe7be316", "sourceName": "test/utils/UpgradeableProxyUtils.t.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IERC165.sol", "lib/forge-std/src/interfaces/IERC20.sol", "lib/forge-std/src/interfaces/IERC721.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/mocks/MockERC20.sol", "lib/forge-std/src/mocks/MockERC721.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/BeaconProxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/UpgradeableBeacon.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "test/utils/Greeter.sol", "test/utils/GreeterProxiable.sol", "test/utils/GreeterV2.sol", "test/utils/GreeterV2Proxiable.sol", "test/utils/NoInitializer.sol", "test/utils/ProxyTestContracts.sol", "test/utils/UpgradeableProxyUtils.sol", "test/utils/WithConstructor.sol"], "versionRequirement": "^0.8.12", "artifacts": {"UpgradeableProxyUtilsTest": {"0.8.12": {"path": "UpgradeableProxyUtils.t.sol/UpgradeableProxyUtilsTest.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}, "test/utils/WithConstructor.sol": {"lastModificationDate": 1730695187946, "contentHash": "bf7aadf2323232651d4fb89f9e7a0b5e", "sourceName": "test/utils/WithConstructor.sol", "compilerSettings": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "london", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "london", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}, "imports": [], "versionRequirement": "^0.8.12", "artifacts": {"WithConstructor": {"0.8.12": {"path": "WithConstructor.sol/WithConstructor.json", "build_id": "c93b552cc771c6720da75394620666be"}}}, "seenByCompiler": true}}, "builds": ["08064f56647916e490d2100b9a051e64", "c93b552cc771c6720da75394620666be"]}