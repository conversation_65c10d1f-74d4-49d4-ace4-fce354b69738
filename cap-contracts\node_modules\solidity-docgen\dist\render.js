"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemPartialName = exports.render = void 0;
const handlebars_1 = __importDefault(require("handlebars"));
const item_type_1 = require("./utils/item-type");
const fs_1 = __importDefault(require("fs"));
function render(site, templates, collapseNewlines) {
    const renderPage = buildRenderer(templates);
    const renderedPages = [];
    for (const page of site.pages) {
        let contents = renderPage(page, { data: { site } });
        if (collapseNewlines) {
            contents = contents.replace(/\n{3,}/g, '\n\n');
        }
        renderedPages.push({
            id: page.id,
            contents,
        });
    }
    return renderedPages;
}
exports.render = render;
const itemPartialName = (item) => (0, item_type_1.itemType)(item).replace(/ /g, '-').toLowerCase();
exports.itemPartialName = itemPartialName;
function itemPartial(item, options) {
    if (!item.__item_context) {
        throw new Error(`Partial 'item' used in unsupported context (not a doc item)`);
    }
    const partial = options?.partials?.[(0, exports.itemPartialName)(item)];
    if (!partial) {
        throw new Error(`Missing partial '${(0, exports.itemPartialName)(item)}'`);
    }
    return partial(item, options);
}
function readmeHelper(H, path, opts) {
    const items = opts.data.root.items;
    const renderedItems = Object.fromEntries(items.map(item => [
        item.name,
        new H.SafeString(H.compile('{{>item}}')(item, opts)),
    ]));
    return new H.SafeString(H.compile(fs_1.default.readFileSync(path, 'utf8'))(renderedItems, opts));
}
function buildRenderer(templates) {
    const pageTemplate = templates.partials?.page;
    if (pageTemplate === undefined) {
        throw new Error(`Missing 'page' template`);
    }
    const H = handlebars_1.default.create();
    for (const [name, getBody] of Object.entries(templates.partials ?? {})) {
        let partial;
        H.registerPartial(name, (...args) => {
            partial ?? (partial = H.compile(getBody()));
            return partial(...args);
        });
    }
    H.registerHelper('readme', (path, opts) => readmeHelper(H, path, opts));
    for (const [name, fn] of Object.entries(templates.helpers ?? {})) {
        H.registerHelper(name, fn);
    }
    H.registerPartial('item', itemPartial);
    return H.compile('{{>page}}');
}
//# sourceMappingURL=render.js.map